import asyncio
import json
import traceback
from pickle import FALSE

from fastapi import Depends, APIRouter, Request
from pydantic import BaseModel
from starlette.responses import StreamingResponse

from api.dependencies.mongo_db import get_mongo_client
from app.ai_models import get_ai_provider_factory
from app.utils.cache_db import get_redis
from app.utils.dto import ChatResponse, ChatRequest, ServiceTypeEnum, ProductNameEnum, ListItemMachshavot
from chat.chat_utils.chat_dto import ChatClaudeRequest
from utils.dto import ListItem
from configs.app_config import SHOULD_PREFORMED_JWT_VALIDATION_ON_CHAT_REQUESTS
from app.middlewares.authorization import handle_request_product
from app.middlewares.exceptions import exception_to_json, internal_error
from chat.chat_core import chat_claude

chat_router = APIRouter()


class ListItemEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, ListItem):
            return obj.dict()
        if isinstance(obj, ListItemMachshavot):
            return obj.dict()
        return super().default(obj)


@chat_router.post("/chat", response_model=ChatResponse)
async def chat(
        request: ChatRequest,
        request_fastapi: Request,
        redis_pool=Depends(get_redis),
        ai_provider=Depends(get_ai_provider_factory),
        mongo_client=Depends(get_mongo_client)

):
    try:
        # if SHOULD_PREFORMED_JWT_VALIDATION_ON_CHAT_REQUESTS:
        #     allow, message, statusCode = handle_request_product(request, request_fastapi)
        #     if not allow:
        #         return ChatResponse(statusCode=statusCode, message=message, chatId=request.chatId)

        async def stream_response():
            try:
                chat_claude_request = ChatClaudeRequest(user_id=request.userId,
                                                        chat_id=request.chatId,
                                                        has_nearests=request.returnNearestAndRelated,
                                                        redis_pool=redis_pool,
                                                        ai_provider=ai_provider,
                                                        mongo_client=mongo_client)

                answer_stream = chat_claude(chat_claude_request)
                async for chunk in answer_stream:
                    if isinstance(chunk, BaseModel):
                        completion_dict = chunk.model_dump()
                        yield f"data: {json.dumps(completion_dict, ensure_ascii=False)}\n\n"
                    elif isinstance(chunk, dict):
                        yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
                    else:
                        yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"

            except Exception as exc:
                yield exception_to_json(exc)

        return StreamingResponse(stream_response(), media_type="text/event-stream")

    except Exception as e:
        return internal_error(e)
