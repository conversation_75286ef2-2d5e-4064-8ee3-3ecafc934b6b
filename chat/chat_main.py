import os
import sys

from fastapi import FastAPI  # Added Request import
from fastapi.middleware.cors import CORSMiddleware

from middlewares.auth_middleware import AuthMiddleware

# Your existing imports
APP_DIR_PATH = os.path.dirname(__file__)
ROOT_DIR_PATH = os.path.dirname(APP_DIR_PATH)

sys.path.append(APP_DIR_PATH)
sys.path.append(ROOT_DIR_PATH)
sys.path.append(os.path.join(ROOT_DIR_PATH, 'app'))


from chat_endpoint import chat_router
from app.middlewares.trace_utils import EndpointNameMiddleware
from app.utils.analytics import FirestoreConnection

import configs.app_config as conf
import json
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

from app.middlewares.logging_utils import app_logger as logger

ENV_FILE_PATH = os.getenv('ENV_FILE_PATH', os.path.join(ROOT_DIR_PATH, 'configs', '.env'))
assert os.path.isfile(ENV_FILE_PATH), f'env file is missing from: {ENV_FILE_PATH}'

# load_dotenv(ENV_FILE_PATH)
print(f'Loading env file from: {ENV_FILE_PATH}')

logger.info('Setting up the app..')

is_dev = conf.ENVIRONMENT in ['local', 'dev']

# Modified FastAPI initialization with timeout
app = FastAPI(
    docs_url="/docs" if is_dev else None,
    redoc_url="/redoc" if is_dev else None
)



@app.on_event("startup")
async def startup_event():

    logger.info('Starting up app dependencies...')
    # Init firebase
    FirestoreConnection()

    file_path = os.path.join(ROOT_DIR_PATH, 'app/utils/error_messages.json')
    with open(file_path, 'r', encoding='utf-8') as file:
        data = json.load(file)
    conf.ERROR_KEYS = list(data.keys())

# Your existing CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    max_age=3600,  # Added max_age
)

app.add_middleware(AuthMiddleware)
app.include_router(chat_router)


if __name__ == "__main__":
    import uvicorn

    logger.info('App is ready and will start serving requests shortly')
    # uvicorn.run(app, host="0.0.0.0", port=8000)  # single worker
    {uvicorn.run("chat_main:app", host="0.0.0.0", port=8000, workers=1)}
