import traceback

from chat_history.services.encrypt_data_manager import Cha<PERSON><PERSON><PERSON>ory<PERSON>anager
from middlewares.logging_utils import app_logger as logger
from redis_db.redis_chat_manager import RedisChatManager
from redis_db.redis_schema import RedisSchema
from utils.pre_chat_dto import SearchTypeEnum, PreChatManager


def create_chat_from_pre_chat_manager(pre_chat_manager: PreChatManager) -> RedisSchema:
    """
    Create a RedisSchema instance from a PreChatManager object

    Args:
        pre_chat_manager: The PreChatManager object

    Returns:
        RedisSchema: The created RedisSchema object
    """
    # Handle query extraction safely
    if hasattr(pre_chat_manager, 'query'):
        if hasattr(pre_chat_manager.query, 'raw_query'):
            query = pre_chat_manager.query.raw_query
        else:
            query = pre_chat_manager.query
    else:
        query = None

    # Handle txt_id extraction safely

    # Handle data_context extraction safely
    data_context = pre_chat_manager.data.model_dump()

    conversation = pre_chat_manager.conversation
    # Handle model_settings extraction safely
    model_settings = pre_chat_manager.model_settings.model_dump()

    # Extract data from pre_chat_manager
    chat_data = RedisSchema(
        user_id=pre_chat_manager.userId,
        chat_id=pre_chat_manager.chatId,
        domain=pre_chat_manager.domain,
        query=query,
        conversation=conversation,
        summaries=pre_chat_manager.summaries,
        data=data_context,
        model_settings=model_settings,
        chat_settings=pre_chat_manager.chat_settings,
        filters=pre_chat_manager.filters,
        citations=pre_chat_manager.citations or [],
        # Generate a title based on the query
    )

    return chat_data


async def save_pre_chat_to_redis(pre_chat_object: PreChatManager):
    """Save data to Redis"""
    try:
        if not pre_chat_object.data.data_raw and not pre_chat_object.data.full_text_content and pre_chat_object.chat_settings.search_type != SearchTypeEnum.trainer:
            raise ValueError("No data to save")
        # Get Redis instance from connection_source
        redis_pool = pre_chat_object.connection_source.redis_instance

        # Create RedisSchema from PreChatManager
        redis_schema = create_chat_from_pre_chat_manager(pre_chat_object)

        # Create a RedisChatManager instance and  Save to Redis
        success = await RedisChatManager(redis_pool).save_chat_in_redis(redis_schema)

        if success:
            logger.info(f'Successfully saved chat data to Redis for chat_id: {pre_chat_object.chatId}')
        else:
            logger.error(f'Failed to save chat data to Redis for chat_id: {pre_chat_object.chatId}')
            raise ValueError("SAVE_IN_REDIS_ERROR")


    except Exception as e:
        logger.error(f'Error saving data to Redis: {e}, chat_id: {pre_chat_object.chatId}')
        raise ValueError("SAVE_IN_REDIS_ERROR")


async def handle_pre_chat_flow(request, connection_source) -> RedisSchema | None:
    """
    Handle the pre-chat flow

    Args:
        request: The request object
        connection_source: The connection source for the PreChatManager

    Returns:
        Tuple of (PreChatManager, bool) where the bool indicates if this is an existing chat
    """
    try:
        logger.info(
            f"handle_pre_chat_flow: Processing request for chat_id={request.chatId}, user_id={request.userId}, domain={request.domain}")

        # Create a RedisChatManager instance
        redis_pool = connection_source.redis_instance
        redis_manager = RedisChatManager(redis_pool)

        # Case 1: New chat (chat_id is None)
        # Case 2: Existing chat in Redis
        # Case 3: Not in Redis, try MongoDB
        logger.info(
            f"handle_pre_chat_flow: Loading chat from Redis for chat_id={request.chatId}, user_id={request.userId}")
        chat_id, chat_data = await redis_manager.load_chat_from_redis(request.chatId, request.userId, request.domain)

        # If chat_id was None, update it in the request
        if not request.chatId:
            logger.info(f"handle_pre_chat_flow: Generated new chat_id={chat_id} for user_id={request.userId}")
            request.chatId = chat_id

        # If we found existing chat data, convert it to a PreChatManager object
        if chat_data:
            logger.info(f"handle_pre_chat_flow: Found existing chat data for chat_id={request.chatId}")
            return chat_data

        try:
            from api.dependencies.encryption_services import (
                get_encryption_manager,
                get_user_key_manager
            )
            from chat_history.services.encrypt_data_manager import ChatHistoryManager

            # Manually resolve dependencies (since we're not in a FastAPI endpoint)
            encryption_manager = get_encryption_manager()
            user_key_manager = get_user_key_manager(encryption_manager)
            history_manager = ChatHistoryManager(
                encryption_manager=encryption_manager,
                mongo_client=connection_source.mongo_client,
                user_key_manager=user_key_manager
            )
            # If no existing chat data, check if the chat history exists
            chat_data = await history_manager.extract_chat_history(request.chatId)
            if chat_data:
                return chat_data
        except Exception as e:
            logger.error(f"Failed to create ChatHistoryManager or extract chat history: {e}")
            # Continue without chat history

        logger.warning(
            f"handle_pre_chat_flow: No existing chat data found for chat_id={request.chatId}, creating new chat")
        return None  # False indicates this is a new chat

    except Exception as e:
        logger.error(
            f"handle_pre_chat_flow: Error handling pre-chat flow for chat_id={request.chatId}, user_id={request.userId}: {e}, {traceback.format_exc()}")
        # Check if the exception is a ValueError with a known error key
        if isinstance(e, ValueError):
            raise
        # Otherwise, wrap it in a generic error
        raise ValueError("EXTRACT_FROM_REDIS_ERROR")
