#!/usr/bin/env python3
"""
Test file for UserKeyManager - Creates key for user ID 123
"""
import asyncio
import os
import sys
from datetime import datetime

# Add the app directory to the path
sys.path.append(os.path.dirname(__file__))

from chat_history.security.encrypt_manger import EncryptionManager
from chat_history.services.user_key_manager import UserKeyManager
from chat_history.services.aws_kms_service import AWSKMSService
import configs.app_config as conf
from middlewares.logging_utils import app_logger as logger


async def test_user_key_manager():
    """Test UserKeyManager with user ID 123"""
    print("🔑 Testing UserKeyManager with User ID 123")
    print("=" * 50)
    
    try:
        # 1. Initialize EncryptionManager
        print("1. Initializing EncryptionManager...")
        master_key = os.environ.get('KMS_MASTER_KEY', 'test_master_key_32_bytes_long_').encode()[:32]
        if len(master_key) < 32:
            master_key = master_key.ljust(32, b'0')
        
        EncryptionManager.initialize(master_key)
        encryption_manager = EncryptionManager.get_instance()
        print("✅ EncryptionManager initialized")
        
        # 2. Initialize KMS service (optional)
        print("2. Initializing KMS service...")
        kms_service = None
        try:
            if hasattr(conf, 'AWS_KMS_KEY_ID') and conf.AWS_KMS_KEY_ID:
                kms_service = AWSKMSService(conf.AWS_KMS_KEY_ID, conf.AWS_KMS_REGION)
                print("✅ KMS service initialized")
            else:
                print("⚠️  KMS not configured, using master key encryption")
        except Exception as e:
            print(f"⚠️  KMS initialization failed: {e}")
            print("   Using master key encryption")
        
        # 3. Initialize UserKeyManager
        print("3. Initializing UserKeyManager...")
        mongo_uri = conf.MONGO_URL.format(conf.MONGO_USER_NAME, conf.MONGO_PASSWORD)
        user_key_manager = UserKeyManager(
            encryption_manager=encryption_manager,
            mongo_uri=mongo_uri,
            db_name=conf.MONGO_DB_HISTORY,
            collection_name=conf.MONGO_USERS_COLLECTION,
            kms_service=kms_service
        )
        print("✅ UserKeyManager initialized")
        
        # 4. Test with user ID 123
        test_user_id = "123"
        print(f"4. Testing with user ID: {test_user_id}")
        
        # Check if user key already exists
        try:
            existing_method = user_key_manager.get_user_encryption_method(test_user_id)
            if existing_method:
                print(f"   User {test_user_id} already has a key with method: {existing_method}")
            else:
                print(f"   User {test_user_id} does not have a key yet")
        except Exception as e:
            print(f"   Could not check existing key: {e}")
        
        # 5. Create or get user key
        print("5. Creating/getting user key...")
        try:
            # This will create the key if it doesn't exist
            user_key, user_key_id = await user_key_manager.get_user_key_and_id(test_user_id)
            print(f"✅ User key retrieved/created:")
            print(f"   Key length: {len(user_key)} bytes")
            print(f"   Key ID: {user_key_id}")
        except Exception as e:
            print(f"❌ Failed to get/create user key: {e}")
            raise
        
        # 6. Verify encryption method
        print("6. Checking encryption method...")
        encryption_method = user_key_manager.get_user_encryption_method(test_user_id)
        print(f"✅ Encryption method: {encryption_method}")
        
        # 7. Test key retrieval again (should use cache/existing)
        print("7. Testing key retrieval again...")
        user_key_2, user_key_id_2 = await user_key_manager.get_user_key_and_id(test_user_id)
        
        # Verify same key
        if user_key == user_key_2 and user_key_id == user_key_id_2:
            print("✅ Same key retrieved (consistency verified)")
        else:
            print("❌ Different key retrieved (inconsistency detected)")
            raise ValueError("Key inconsistency detected")
        
        # 8. Test individual methods
        print("8. Testing individual methods...")
        
        # Test get_user_key
        individual_key = await user_key_manager.get_user_key(test_user_id)
        if individual_key == user_key:
            print("✅ get_user_key() returns same key")
        else:
            print("❌ get_user_key() returns different key")
        
        # Test get_user_key_id
        individual_key_id = await user_key_manager.get_user_key_id(test_user_id)
        if individual_key_id == user_key_id:
            print("✅ get_user_key_id() returns same ID")
        else:
            print("❌ get_user_key_id() returns different ID")
        
        # 9. Test encryption/decryption with the key
        print("9. Testing encryption/decryption...")
        test_data = "Hello, this is test data for user 123!"
        
        # Encrypt with EncryptionManager using user key
        from utils.conversation_manager import ConversationItem
        test_conversation = [
            ConversationItem(
                index=0,
                role="user",
                content=test_data,
                timestamp=datetime.now().isoformat()
            )
        ]
        
        encrypted_data = encryption_manager.encrypt_conversation_with_user_key(
            test_conversation, user_key
        )
        print(f"✅ Data encrypted (size: {len(encrypted_data)} bytes)")
        
        # Decrypt
        decrypted_conversation = encryption_manager.decrypt_conversation_with_user_key(
            encrypted_data, user_key
        )
        
        if decrypted_conversation[0].content == test_data:
            print("✅ Data decrypted successfully")
        else:
            print("❌ Decryption failed")
            raise ValueError("Decryption verification failed")
        
        # 10. Test cache invalidation
        print("10. Testing cache invalidation...")
        cache_result = await user_key_manager.invalidate_user_key_cache(test_user_id)
        print(f"✅ Cache invalidation result: {cache_result}")
        
        print("\n🎉 All UserKeyManager tests passed!")
        print("=" * 50)
        print(f"✅ User ID 123 has a working encryption key")
        print(f"✅ Key ID: {user_key_id}")
        print(f"✅ Encryption method: {encryption_method}")
        print(f"✅ Key length: {len(user_key)} bytes")
        print(f"✅ Encryption/decryption working")
        
        if kms_service:
            print("✅ KMS integration available")
        else:
            print("⚠️  Using master key encryption (KMS not configured)")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function"""
    success = await test_user_key_manager()
    if success:
        print("\n🔑 UserKeyManager test completed successfully!")
        print("User ID 123 now has a working encryption key.")
    else:
        print("\n❌ UserKeyManager test failed.")
        sys.exit(1)


if __name__ == "__main__":
    # Load environment variables
    from dotenv import load_dotenv
    env_file = os.path.join(os.path.dirname(__file__), '..', 'configs', '.env')
    if os.path.exists(env_file):
        load_dotenv(env_file)
    
    asyncio.run(main())
