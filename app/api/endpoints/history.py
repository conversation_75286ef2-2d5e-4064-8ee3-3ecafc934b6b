from fastapi import APIRouter, Depends, Query, HTTPException, BackgroundTasks, Request, security
from fastapi.responses import JSONResponse
from motor.motor_asyncio import AsyncIOMotorClient
from pydantic import BaseModel
from typing import Dict, Any

from api.dependencies.mongo_db import get_mongo_client
from api.dependencies.encryption_services import get_chat_history_manager_dep
from chat_history.services.chat_history_service import (
    get_user_chats,
    get_chat_by_id,
    update_chat_title,
    delete_chat_by_id
)
from chat_history.utils.history_utils import OrderByEnum, validate_order_field
from middlewares.logging_utils import app_logger as logger

from fastapi import HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

security_rs = HTTPBearer()


def token_auth(credentials: HTTPAuthorizationCredentials = Depends(security_rs)):
    token = credentials.credentials
    # כאן אתה יכול לבדוק את התוקף אם בא לך
    return token
#dependencies=[Depends(token_auth)]

history_router = APIRouter(tags=["History"]
                           )  # Ensure user claims are validated for all routes


class UpdateTitleRequest(BaseModel):
    title: str


@history_router.get("/history/{user_id}")
async def get_user_chats_list(
        user_id: str,
        request: Request,
        offset: int = Query(0, ge=0, description="Number of records to skip"),
        limit: int = Query(20, ge=1, le=100, description="Maximum number of records to return"),
        order_by: OrderByEnum = Query(OrderByEnum.UPDATED, description="Order by: 'updated' or 'created'"),
        mongo_client: AsyncIOMotorClient = Depends(get_mongo_client)
):
    """Get user's chat history with pagination. Authentication handled by middleware."""
    try:
        if not validate_order_field(order_by):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid order field: {order_by}. Valid options: 'updated', 'created'"
            )

        # Get user_id from middleware (automatically validated)
        # user_id = get_user_id(request)

        chats_data = await get_user_chats(
            mongo_client=mongo_client,
            user_id=user_id,
            offset=offset,
            limit=limit,
            order_by=order_by
        )

        return JSONResponse(status_code=200, content=chats_data)

    except HTTPException:
        raise
    except ConnectionError as e:
        logger.error(f"Database connection error: {e}")
        raise HTTPException(status_code=503, detail="Database connection error")
    except Exception as e:
        logger.error(f"Error in get_user_chats_list: {e}")
        raise HTTPException(status_code=500, detail="Internal error")


@history_router.get("/chat/{chat_id}")
async def get_chat(
        request: Request,
        chat_id: str,
        user_id: str = Query(..., description="User ID for security"),
        mongo_client: AsyncIOMotorClient = Depends(get_mongo_client),
        chat_history_manager = Depends(get_chat_history_manager_dep)
):
    """Get chat by chat_id with automatic decryption. Authentication handled by middleware."""
    try:
        # Get user_id from middleware (automatically validated)
        # user_id = get_user_id(request)

        # Get chat document with decryption support
        chat_document = await get_chat_by_id(
            mongo_client,
            chat_id,
            user_id=user_id,
            chat_history_manager=chat_history_manager
        )

        # Validate that the authenticated user owns this chat
        if chat_document.get('user_id') != user_id:
            raise HTTPException(status_code=403, detail="Access denied: You don't own this chat")

        if not chat_document:
            raise HTTPException(status_code=404, detail=f"Chat not found: {chat_id}")

        return JSONResponse(status_code=200, content={"chat": chat_document})

    except HTTPException:
        raise
    except ConnectionError as e:
        logger.error(f"Database connection error: {e}")
        raise HTTPException(status_code=503, detail="Database connection error")
    except Exception as e:
        logger.error(f"Error in get_chat: {e}")
        raise HTTPException(status_code=500, detail="Internal error")


@history_router.put("/chat/{chat_id}/title")
async def update_chat_title_endpoint(
        request: Request,
        chat_id: str,
        title_request: UpdateTitleRequest,
        user_id: str = Query(..., description="User ID for security"),
        mongo_client: AsyncIOMotorClient = Depends(get_mongo_client)
):
    """Update chat title. Authentication handled by middleware."""
    try:
        # Get user_id from middleware (automatically validated)
        # user_id = get_user_id(request)

        # Update chat title with user_id validation (service function handles this)
        success = await update_chat_title(
            mongo_client,
            chat_id,
            title_request.title,
            user_id  # This ensures only the owner can update
        )

        if not success:
            raise HTTPException(status_code=404, detail=f"Chat not found: {chat_id}")

        return JSONResponse(
            status_code=200,
            content={
                "message": "Chat title updated successfully",
                "chat_id": chat_id,
                "new_title": title_request.title
            }
        )

    except HTTPException:
        raise
    except ConnectionError as e:
        logger.error(f"Database connection error: {e} for chat_id={chat_id}")
        raise HTTPException(status_code=503, detail="Database connection error")
    except Exception as e:
        logger.error(f"Error in update_chat_title_endpoint: {e} for chat_id={chat_id}")
        raise HTTPException(status_code=500, detail="Internal error")


@history_router.delete("/chat/{chat_id}")
async def delete_chat(
        request: Request,
        chat_id: str,
        user_id: str = Query(..., description="User ID for security"),
        mongo_client: AsyncIOMotorClient = Depends(get_mongo_client)
):
    """Delete chat by chat_id. Authentication handled by middleware."""
    try:
        # Get user_id from middleware (automatically validated)
        # user_id = get_user_id(request)

        # Delete chat with user_id validation (service function handles this)
        success = await delete_chat_by_id(
            mongo_client,
            chat_id,
            user_id  # This ensures only the owner can delete
        )

        if not success:
            raise HTTPException(status_code=404, detail=f"Chat not found: {chat_id}")

        return JSONResponse(status_code=200, content={"message": "Chat deleted successfully"})

    except HTTPException:
        raise
    except ConnectionError as e:
        logger.error(f"Database connection error: {e} for chat_id={chat_id}")
        raise HTTPException(status_code=503, detail="Database connection error")
    except Exception as e:
        logger.error(f"Error in delete_chat: {e} for chat_id={chat_id}")
        raise HTTPException(status_code=500, detail="Internal error")
