"""
Test endpoints for encryption functionality
"""
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Optional

from api.dependencies.encryption_services import (
    get_encryption_manager_dep,
    get_user_key_manager_dep,
    get_chat_history_manager_dep
)
from middlewares.logging_utils import app_logger as logger

# Create router
encryption_test_router = APIRouter(prefix="/encryption-test", tags=["encryption-test"])


class EncryptionTestResponse(BaseModel):
    success: bool
    message: str
    details: Optional[dict] = None


@encryption_test_router.post("/create-user-key/{user_id}")
async def create_user_key_test(
    user_id: str,
    user_key_manager = Depends(get_user_key_manager_dep)
):
    """Test endpoint to create a user encryption key"""
    try:
        if not user_key_manager:
            raise HTTPException(
                status_code=503, 
                detail="UserKeyManager not available. Encryption services may not be initialized."
            )
        
        # Create user key
        result = user_key_manager.create_user_key(user_id)
        
        # Get encryption method
        encryption_method = user_key_manager.get_user_encryption_method(user_id)
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": f"User key created successfully for user {user_id}",
                "details": {
                    "user_id": user_id,
                    "encryption_method": encryption_method,
                    "key_created": True
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Error creating user key for {user_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create user key: {str(e)}"
        )


@encryption_test_router.get("/user-key-status/{user_id}")
async def get_user_key_status(
    user_id: str,
    user_key_manager = Depends(get_user_key_manager_dep)
):
    """Test endpoint to check user key status"""
    try:
        if not user_key_manager:
            raise HTTPException(
                status_code=503, 
                detail="UserKeyManager not available. Encryption services may not be initialized."
            )
        
        # Check if user key exists
        try:
            user_key_id = user_key_manager.get_user_key_id(user_id)
            encryption_method = user_key_manager.get_user_encryption_method(user_id)
            key_exists = True
        except ValueError:
            user_key_id = None
            encryption_method = None
            key_exists = False
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": f"User key status for {user_id}",
                "details": {
                    "user_id": user_id,
                    "key_exists": key_exists,
                    "user_key_id": user_key_id,
                    "encryption_method": encryption_method
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Error checking user key status for {user_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to check user key status: {str(e)}"
        )


@encryption_test_router.get("/encryption-status")
async def get_encryption_status(
    encryption_manager = Depends(get_encryption_manager_dep),
    user_key_manager = Depends(get_user_key_manager_dep),
    chat_history_manager = Depends(get_chat_history_manager_dep)
):
    """Test endpoint to check overall encryption system status"""
    try:
        status = {
            "encryption_manager": encryption_manager is not None,
            "user_key_manager": user_key_manager is not None,
            "chat_history_manager": chat_history_manager is not None,
        }
        
        # Check KMS availability if user_key_manager exists
        kms_available = False
        if user_key_manager:
            kms_available = user_key_manager.kms_service is not None
        
        status["kms_service"] = kms_available
        
        all_services_available = all([
            status["encryption_manager"],
            status["user_key_manager"], 
            status["chat_history_manager"]
        ])
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": "Encryption system status",
                "details": {
                    "all_services_available": all_services_available,
                    "services": status,
                    "encryption_mode": "KMS" if kms_available else "MASTER_KEY"
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Error checking encryption status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to check encryption status: {str(e)}"
        )


@encryption_test_router.post("/test-conversation-encryption/{user_id}")
async def test_conversation_encryption(
    user_id: str,
    encryption_manager = Depends(get_encryption_manager_dep),
    user_key_manager = Depends(get_user_key_manager_dep)
):
    """Test endpoint to verify conversation encryption/decryption works"""
    try:
        if not encryption_manager or not user_key_manager:
            raise HTTPException(
                status_code=503,
                detail="Encryption services not available"
            )
        
        # Ensure user has a key
        try:
            user_key = await user_key_manager.get_user_key(user_id)
        except ValueError:
            # Create user key if it doesn't exist
            user_key_manager.create_user_key(user_id)
            user_key = await user_key_manager.get_user_key(user_id)
        
        # Create test conversation
        from utils.conversation_manager import ConversationItem
        from datetime import datetime
        
        test_conversation = [
            ConversationItem(
                index=0,
                role="user",
                content="Test message for encryption",
                timestamp=datetime.now().isoformat()
            ),
            ConversationItem(
                index=1,
                role="assistant",
                content="Test response for encryption",
                timestamp=datetime.now().isoformat()
            )
        ]
        
        # Test encryption
        encrypted_data = encryption_manager.encrypt_conversation_with_user_key(
            test_conversation, user_key
        )
        
        # Test decryption
        decrypted_conversation = encryption_manager.decrypt_conversation_with_user_key(
            encrypted_data, user_key
        )
        
        # Verify integrity
        success = (
            len(decrypted_conversation) == len(test_conversation) and
            decrypted_conversation[0].content == test_conversation[0].content and
            decrypted_conversation[1].content == test_conversation[1].content
        )
        
        return JSONResponse(
            status_code=200,
            content={
                "success": success,
                "message": "Conversation encryption test completed",
                "details": {
                    "user_id": user_id,
                    "original_items": len(test_conversation),
                    "decrypted_items": len(decrypted_conversation),
                    "encrypted_size": len(encrypted_data),
                    "integrity_verified": success
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Error testing conversation encryption for {user_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Conversation encryption test failed: {str(e)}"
        )
