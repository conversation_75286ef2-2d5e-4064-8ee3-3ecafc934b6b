"""
Dependency injection for encryption services
"""
from fastapi import Depends
from typing import Op<PERSON>
from motor.motor_asyncio import AsyncIOMotorClient

from chat_history.security.encrypt_manger import EncryptionManager
from chat_history.services.user_key_manager import UserKeyManager
from chat_history.services.encrypt_data_manager import Chat<PERSON><PERSON>ory<PERSON>anager
from chat_history.services.aws_kms_service import AWSKMSService
from api.dependencies.mongo_db import get_mongo_client
import configs.app_config as conf
from middlewares.logging_utils import app_logger as logger


def get_encryption_manager() -> EncryptionManager:
    """
    Get the EncryptionManager instance

    Returns:
        EncryptionManager: The singleton encryption manager instance

    Raises:
        RuntimeError: If EncryptionManager is not initialized
    """
    try:
        return EncryptionManager.get_instance()
    except RuntimeError as e:
        raise RuntimeError("EncryptionManager not initialized. Ensure init_encryption_services() is called on startup.") from e


def get_user_key_manager() -> UserKeyManager:
    """
    Create UserKeyManager instance when needed

    Returns:
        UserKeyManager: New instance of UserKeyManager
    """
    encryption_manager = get_encryption_manager()

    # Initialize KMS service (optional)
    kms_service = None
    try:
        if hasattr(conf, 'AWS_KMS_KEY_ID') and conf.AWS_KMS_KEY_ID:
            kms_service = AWSKMSService(conf.AWS_KMS_KEY_ID, conf.AWS_KMS_REGION)
    except Exception as e:
        logger.warning(f"Failed to initialize KMS service: {e}")

    # Create UserKeyManager
    mongo_uri = conf.MONGO_URL.format(conf.MONGO_USER_NAME, conf.MONGO_PASSWORD)
    return UserKeyManager(
        encryption_manager=encryption_manager,
        mongo_uri=mongo_uri,
        db_name=conf.MONGO_DB_HISTORY,
        collection_name=conf.MONGO_USERS_COLLECTION,
        kms_service=kms_service
    )


async def get_chat_history_manager(
    mongo_client: AsyncIOMotorClient = Depends(get_mongo_client)
) -> ChatHistoryManager:
    """
    Create ChatHistoryManager instance when needed

    Returns:
        ChatHistoryManager: New instance of ChatHistoryManager
    """
    encryption_manager = get_encryption_manager()
    user_key_manager = get_user_key_manager()

    return ChatHistoryManager(
        encryption_manager=encryption_manager,
        mongo_client=mongo_client,
        user_key_manager=user_key_manager
    )


# FastAPI dependency functions
async def get_encryption_manager_dep() -> EncryptionManager:
    """FastAPI dependency for EncryptionManager"""
    return get_encryption_manager()


async def get_user_key_manager_dep() -> UserKeyManager:
    """FastAPI dependency for UserKeyManager"""
    return get_user_key_manager()


async def get_chat_history_manager_dep(
    mongo_client: AsyncIOMotorClient = Depends(get_mongo_client)
) -> ChatHistoryManager:
    """FastAPI dependency for ChatHistoryManager"""
    return await get_chat_history_manager(mongo_client)
