"""
Dependency injection for encryption services
"""
from fastapi import Depends
from typing import Optional

from chat_history.security.encrypt_manger import EncryptionManager
from chat_history.services.user_key_manager import UserKeyManager
from chat_history.services.encrypt_data_manager import Chat<PERSON><PERSON>oryManager


def get_encryption_manager() -> EncryptionManager:
    """
    Get the EncryptionManager instance
    
    Returns:
        EncryptionManager: The singleton encryption manager instance
        
    Raises:
        RuntimeError: If EncryptionManager is not initialized
    """
    try:
        return EncryptionManager.get_instance()
    except RuntimeError as e:
        raise RuntimeError("EncryptionManager not initialized. Ensure init_encryption_services() is called on startup.") from e


def get_user_key_manager() -> Optional[UserKeyManager]:
    """
    Get the UserKeyManager instance from main module
    
    Returns:
        Optional[UserKeyManager]: The user key manager instance or None if not initialized
    """
    try:
        from main import get_user_key_manager as get_ukm
        return get_ukm()
    except (ImportError, RuntimeError):
        return None


def get_chat_history_manager() -> Optional[ChatHistoryManager]:
    """
    Get the ChatHistoryManager instance from main module
    
    Returns:
        Optional[ChatHistoryManager]: The chat history manager instance or None if not initialized
    """
    try:
        from main import get_chat_history_manager as get_chm
        return get_chm()
    except (ImportError, RuntimeError):
        return None


# FastAPI dependency functions
async def get_encryption_manager_dep() -> EncryptionManager:
    """FastAPI dependency for EncryptionManager"""
    return get_encryption_manager()


async def get_user_key_manager_dep() -> Optional[UserKeyManager]:
    """FastAPI dependency for UserKeyManager"""
    return get_user_key_manager()


async def get_chat_history_manager_dep() -> Optional[ChatHistoryManager]:
    """FastAPI dependency for ChatHistoryManager"""
    return get_chat_history_manager()
