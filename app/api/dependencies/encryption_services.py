"""
Dependency injection for encryption services
"""
from fastapi import Depends
from motor.motor_asyncio import AsyncIOMotorClient

from chat_history.security.encrypt_manger import EncryptionManager
from chat_history.services.user_key_manager import UserKeyManager
from chat_history.services.encrypt_data_manager import ChatHistoryManager
from chat_history.services.aws_kms_service import AWSKMSService
from api.dependencies.mongo_db import get_mongo_client
import configs.app_config as conf
from middlewares.logging_utils import app_logger as logger


async def get_chat_history_manager_dep(
    mongo_client: AsyncIOMotorClient = Depends(get_mongo_client)
) -> ChatHistoryManager:
    """
    FastAPI dependency for ChatHistoryManager with required EncryptionManager

    Returns:
        ChatHistoryManager: New instance with all required dependencies
    """
    # Get EncryptionManager (required)
    try:
        encryption_manager = EncryptionManager.get_instance()
    except RuntimeError as e:
        raise RuntimeError("EncryptionManager not initialized. Ensure init_encryption_services() is called on startup.") from e

    # Create UserKeyManager (optional but recommended)
    user_key_manager = None
    try:
        # Initialize KMS service (optional)
        kms_service = None
        try:
            if hasattr(conf, 'AWS_KMS_KEY_ID') and conf.AWS_KMS_KEY_ID:
                kms_service = AWSKMSService(conf.AWS_KMS_KEY_ID, conf.AWS_KMS_REGION)
        except Exception as e:
            logger.warning(f"Failed to initialize KMS service: {e}")

        # Create UserKeyManager
        mongo_uri = conf.MONGO_URL.format(conf.MONGO_USER_NAME, conf.MONGO_PASSWORD)
        user_key_manager = UserKeyManager(
            encryption_manager=encryption_manager,
            mongo_uri=mongo_uri,
            db_name=conf.MONGO_DB_HISTORY,
            collection_name=conf.MONGO_USERS_COLLECTION,
            kms_service=kms_service
        )
    except Exception as e:
        logger.error(f"Failed to create UserKeyManager: {e}")
        # Continue without UserKeyManager - encryption will still work with master key

    # Create ChatHistoryManager with required EncryptionManager
    return ChatHistoryManager(
        encryption_manager=encryption_manager,
        mongo_client=mongo_client,
        user_key_manager=user_key_manager
    )
