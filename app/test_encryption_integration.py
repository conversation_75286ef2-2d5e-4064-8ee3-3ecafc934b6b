#!/usr/bin/env python3
"""
Test script to verify encryption integration is working correctly
"""
import asyncio
import os
import sys
from datetime import datetime
from typing import List

# Add the app directory to the path
sys.path.append(os.path.dirname(__file__))

from chat_history.security.encrypt_manger import EncryptionManager
from chat_history.services.user_key_manager import UserKeyManager
from chat_history.services.encrypt_data_manager import ChatHistoryManager
from chat_history.services.aws_kms_service import AWSKMSService
from utils.conversation_manager import ConversationItem
from redis_db.redis_schema import RedisSchema
from chat_history.schema.chat_settings_models import ChatSettings
from chat_history.schema.model_settings_models import ModelSettings
from configs import app_config as conf
from middlewares.logging_utils import app_logger as logger


async def test_encryption_workflow():
    """Test the complete encryption workflow"""
    print("🔐 Testing Encryption Integration Workflow")
    print("=" * 50)
    
    try:
        # 1. Initialize EncryptionManager
        print("1. Initializing EncryptionManager...")
        master_key = os.environ.get('KMS_MASTER_KEY', 'test_master_key_32_bytes_long_').encode()[:32]
        if len(master_key) < 32:
            master_key = master_key.ljust(32, b'0')
        
        EncryptionManager.initialize(master_key)
        encryption_manager = EncryptionManager.get_instance()
        print("✅ EncryptionManager initialized successfully")
        
        # 2. Initialize KMS Service (optional)
        print("2. Initializing KMS Service...")
        kms_service = None
        try:
            if hasattr(conf, 'AWS_KMS_KEY_ID') and conf.AWS_KMS_KEY_ID:
                kms_service = AWSKMSService(conf.AWS_KMS_KEY_ID, conf.AWS_KMS_REGION)
                print("✅ KMS Service initialized successfully")
            else:
                print("⚠️  KMS Service not configured, using master key encryption")
        except Exception as e:
            print(f"⚠️  KMS Service failed to initialize: {e}")
            print("   Continuing with master key encryption...")
        
        # 3. Initialize UserKeyManager
        print("3. Initializing UserKeyManager...")
        mongo_uri = conf.MONGO_URL.format(conf.MONGO_USER_NAME, conf.MONGO_PASSWORD)
        user_key_manager = UserKeyManager(
            encryption_manager=encryption_manager,
            mongo_uri=mongo_uri,
            db_name=conf.MONGO_DB_HISTORY,
            collection_name=conf.MONGO_USERS_COLLECTION,
            kms_service=kms_service
        )
        print("✅ UserKeyManager initialized successfully")
        
        # 4. Test user key creation
        print("4. Testing user key creation...")
        test_user_id = "test_user_12345"
        
        # Create user key
        user_key_data = user_key_manager.create_user_key(test_user_id)
        print(f"✅ User key created for user {test_user_id}")
        
        # Retrieve user key
        decrypted_user_key = await user_key_manager.get_user_key(test_user_id)
        print(f"✅ User key retrieved and decrypted (length: {len(decrypted_user_key)} bytes)")
        
        # 5. Test conversation encryption
        print("5. Testing conversation encryption...")
        
        # Create sample conversation
        test_conversation = [
            ConversationItem(
                index=0,
                role="user",
                content="שלום, איך אני יכול לקבל עזרה משפטית?",
                timestamp=datetime.now().isoformat()
            ),
            ConversationItem(
                index=1,
                role="assistant", 
                content="שלום! אני כאן לעזור לך בשאלות משפטיות. על איזה נושא תרצה לקבל מידע?",
                timestamp=datetime.now().isoformat()
            )
        ]
        
        # Test encryption with user key
        encrypted_data = encryption_manager.encrypt_conversation_with_user_key(
            test_conversation, decrypted_user_key
        )
        print(f"✅ Conversation encrypted (size: {len(encrypted_data)} bytes)")
        
        # Test decryption
        decrypted_conversation = encryption_manager.decrypt_conversation_with_user_key(
            encrypted_data, decrypted_user_key
        )
        print(f"✅ Conversation decrypted ({len(decrypted_conversation)} items)")
        
        # Verify data integrity
        assert len(decrypted_conversation) == len(test_conversation)
        assert decrypted_conversation[0].content == test_conversation[0].content
        assert decrypted_conversation[1].content == test_conversation[1].content
        print("✅ Data integrity verified")
        
        # 6. Test hash generation and verification
        print("6. Testing hash generation and verification...")
        conversation_hash = encryption_manager.generate_conversation_hash(test_conversation)
        print(f"✅ Hash generated: {conversation_hash[:16]}...")
        
        hash_valid = encryption_manager.verify_conversation_hash(decrypted_conversation, conversation_hash)
        assert hash_valid, "Hash verification failed"
        print("✅ Hash verification successful")
        
        # 7. Test ChatHistoryManager integration
        print("7. Testing ChatHistoryManager integration...")
        
        # Initialize with mock mongo client (for testing)
        from api.dependencies.mongo_db import get_mongo_client
        mongo_client = await get_mongo_client()
        
        chat_history_manager = ChatHistoryManager(
            encryption_manager=encryption_manager,
            mongo_client=mongo_client,
            user_key_manager=user_key_manager
        )
        print("✅ ChatHistoryManager initialized")
        
        # Create test Redis schema
        test_redis_data = RedisSchema(
            user_id=test_user_id,
            chat_id="test_chat_12345",
            domain="test",
            conversation=test_conversation,
            summaries=[],
            model_settings=ModelSettings(),
            chat_settings=ChatSettings(title="Test Chat"),
            filters={},
            citations=[]
        )
        
        # Test storing encrypted chat
        print("8. Testing encrypted chat storage...")
        result = await chat_history_manager.store_chat(test_redis_data)
        print(f"✅ Chat stored successfully (upserted: {result.upserted_id is not None})")
        
        # Test retrieving and decrypting chat
        print("9. Testing encrypted chat retrieval...")
        retrieved_chat = await chat_history_manager.extract_chat_history(
            test_redis_data.chat_id, test_user_id
        )
        print(f"✅ Chat retrieved and decrypted ({len(retrieved_chat.conversation)} items)")
        
        # Verify retrieved data
        assert len(retrieved_chat.conversation) == len(test_conversation)
        assert retrieved_chat.conversation[0].content == test_conversation[0].content
        print("✅ Retrieved data integrity verified")
        
        print("\n🎉 All encryption integration tests passed!")
        print("=" * 50)
        print("✅ EncryptionManager: Working")
        print("✅ UserKeyManager: Working") 
        print("✅ ChatHistoryManager: Working")
        print("✅ Conversation Encryption: Working")
        print("✅ Hash Verification: Working")
        print("✅ MongoDB Integration: Working")
        
        if kms_service:
            print("✅ AWS KMS Integration: Working")
        else:
            print("⚠️  AWS KMS Integration: Not configured (using master key)")
            
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function"""
    success = await test_encryption_workflow()
    if success:
        print("\n🔐 Encryption system is ready for production!")
    else:
        print("\n❌ Encryption system needs attention before production use.")
        sys.exit(1)


if __name__ == "__main__":
    # Load environment variables
    from dotenv import load_dotenv
    env_file = os.path.join(os.path.dirname(__file__), '..', 'configs', '.env')
    load_dotenv(env_file)
    
    asyncio.run(main())
