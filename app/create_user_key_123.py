#!/usr/bin/env python3
"""
Simple script to create encryption key for user ID 123
"""
import asyncio
import os
import sys

# Add the app directory to the path
sys.path.append(os.path.dirname(__file__))

from chat_history.utils.encryption_startup import init_encryption_manager
from api.dependencies.encryption_services import get_user_key_manager
from middlewares.logging_utils import app_logger as logger


async def create_key_for_user_123():
    """Create encryption key for user ID 123"""
    print("🔑 Creating encryption key for user ID 123")
    print("=" * 40)
    
    try:
        # 1. Initialize EncryptionManager (like app startup)
        print("1. Initializing encryption services...")
        await init_encryption_manager()
        print("✅ EncryptionManager ready")
        
        # 2. Get UserKeyManager
        print("2. Getting UserKeyManager...")
        user_key_manager = get_user_key_manager()
        print("✅ UserKeyManager ready")
        
        # 3. Create/get key for user 123
        user_id = "123"
        print(f"3. Creating/getting key for user {user_id}...")
        
        # This will automatically create the key if it doesn't exist
        user_key, user_key_id = await user_key_manager.get_user_key_and_id(user_id)
        
        print("✅ Success!")
        print(f"   User ID: {user_id}")
        print(f"   Key ID: {user_key_id}")
        print(f"   Key length: {len(user_key)} bytes")
        
        # 4. Check encryption method
        encryption_method = user_key_manager.get_user_encryption_method(user_id)
        print(f"   Encryption method: {encryption_method}")
        
        # 5. Verify key works
        print("4. Verifying key works...")
        
        # Try to get the key again
        test_key = await user_key_manager.get_user_key(user_id)
        if len(test_key) == 32:  # AES-256 key should be 32 bytes
            print("✅ Key verification successful")
        else:
            print(f"⚠️  Key length unexpected: {len(test_key)} bytes")
        
        print("\n🎉 User 123 encryption key created successfully!")
        print("=" * 40)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Failed to create key: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main function"""
    success = await create_key_for_user_123()
    if success:
        print("\n✅ Done! User 123 now has an encryption key.")
    else:
        print("\n❌ Failed to create encryption key for user 123.")
        sys.exit(1)


if __name__ == "__main__":
    # Load environment variables
    from dotenv import load_dotenv
    env_file = os.path.join(os.path.dirname(__file__), '..', 'configs', '.env')
    if os.path.exists(env_file):
        load_dotenv(env_file)
    
    asyncio.run(main())
