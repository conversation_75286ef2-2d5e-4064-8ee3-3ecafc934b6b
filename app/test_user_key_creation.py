#!/usr/bin/env python3
"""
Test script to verify automatic user key creation works
"""
import asyncio
import os
import sys
from datetime import datetime

# Add the app directory to the path
sys.path.append(os.path.dirname(__file__))

from chat_history.security.encrypt_manger import EncryptionManager
from api.dependencies.encryption_services import get_encryption_manager, get_user_key_manager
from utils.conversation_manager import ConversationItem
from middlewares.logging_utils import app_logger as logger


async def test_automatic_user_key_creation():
    """Test that user keys are created automatically when needed"""
    print("🔑 Testing Automatic User Key Creation")
    print("=" * 50)
    
    try:
        # 1. Initialize EncryptionManager (simulate app startup)
        print("1. Initializing EncryptionManager...")
        master_key = os.environ.get('KMS_MASTER_KEY', 'test_master_key_32_bytes_long_').encode()[:32]
        if len(master_key) < 32:
            master_key = master_key.ljust(32, b'0')
        
        EncryptionManager.initialize(master_key)
        print("✅ EncryptionManager initialized")
        
        # 2. Get dependencies
        print("2. Getting dependencies...")
        encryption_manager = get_encryption_manager()
        user_key_manager = get_user_key_manager(encryption_manager)
        print("✅ Dependencies created")
        
        # 3. Test with a new user (should auto-create key)
        test_user_id = f"test_user_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        print(f"3. Testing with new user: {test_user_id}")
        
        # This should automatically create a user key
        print("   Getting user key (should auto-create)...")
        user_key = await user_key_manager.get_user_key(test_user_id)
        print(f"✅ User key retrieved (length: {len(user_key)} bytes)")
        
        # 4. Verify user key ID can be retrieved
        print("4. Getting user key ID...")
        user_key_id = user_key_manager.get_user_key_id(test_user_id)
        print(f"✅ User key ID: {user_key_id}")
        
        # 5. Test encryption with the auto-created key
        print("5. Testing conversation encryption with auto-created key...")
        test_conversation = [
            ConversationItem(
                index=0,
                role="user",
                content="שלום, זה בדיקה של הצפנה אוטומטית",
                timestamp=datetime.now().isoformat()
            ),
            ConversationItem(
                index=1,
                role="assistant",
                content="שלום! המפתח נוצר אוטומטית והצפנה עובדת כראוי",
                timestamp=datetime.now().isoformat()
            )
        ]
        
        # Encrypt conversation
        encrypted_data = encryption_manager.encrypt_conversation_with_user_key(
            test_conversation, user_key
        )
        print(f"✅ Conversation encrypted (size: {len(encrypted_data)} bytes)")
        
        # Decrypt conversation
        decrypted_conversation = encryption_manager.decrypt_conversation_with_user_key(
            encrypted_data, user_key
        )
        print(f"✅ Conversation decrypted ({len(decrypted_conversation)} items)")
        
        # 6. Verify data integrity
        print("6. Verifying data integrity...")
        assert len(decrypted_conversation) == len(test_conversation)
        assert decrypted_conversation[0].content == test_conversation[0].content
        assert decrypted_conversation[1].content == test_conversation[1].content
        print("✅ Data integrity verified")
        
        # 7. Test getting the same key again (should use existing)
        print("7. Testing key retrieval for existing user...")
        user_key_2 = await user_key_manager.get_user_key(test_user_id)
        assert user_key == user_key_2, "Retrieved key should be the same"
        print("✅ Same key retrieved for existing user")
        
        # 8. Check encryption method
        print("8. Checking encryption method...")
        encryption_method = user_key_manager.get_user_encryption_method(test_user_id)
        print(f"✅ Encryption method: {encryption_method}")
        
        print("\n🎉 All automatic user key creation tests passed!")
        print("=" * 50)
        print("✅ User keys are created automatically when needed")
        print("✅ Encryption/decryption works with auto-created keys")
        print("✅ Data integrity is maintained")
        print("✅ Existing keys are reused properly")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function"""
    success = await test_automatic_user_key_creation()
    if success:
        print("\n🔑 Automatic user key creation is working perfectly!")
    else:
        print("\n❌ Automatic user key creation needs attention.")
        sys.exit(1)


if __name__ == "__main__":
    # Load environment variables
    from dotenv import load_dotenv
    env_file = os.path.join(os.path.dirname(__file__), '..', 'configs', '.env')
    load_dotenv(env_file)
    
    asyncio.run(main())
