"""
Chat History Authentication Dependencies

Now simplified - middleware handles authentication automatically.
These functions are kept for backward compatibility.
"""

# Import from middleware

# Simple helper functions for backward compatibility
def get_user_id_from_auth(user_info):
    """Legacy function - extract user_id from user_info dict"""
    return user_info.get("user_id", "")

def is_premium_user(user_info):
    """Legacy function - check if user is premium (now based on claims only)"""
    claims = user_info.get("claims", {})
    return claims.get("subscription_type", "") in ["premium", "pro", "enterprise"]

# Re-export for backward compatibility
__all__ = [
    "get_user_id_from_auth",
    "is_premium_user"
]
