"""
Simple UserKeyManager with only 2 essential functions:
1. create_user_key - Creates a new encryption key for a user
2. get_user_key - Gets the decrypted encryption key for a user
"""
import secrets
from typing import Optional
from datetime import datetime
import pytz

from pymongo import MongoClient
from bson.binary import Binary

from chat_history.security.encrypt_manger import EncryptionManager
from chat_history.services.aws_kms_service import AWSKMSService
from chat_history.services.user_key_cache import UserKeyCacheService
from middlewares.logging_utils import app_logger as logger


class UserKeyManager:
    """Simple UserKeyManager with only 2 essential functions"""

    def __init__(self, encryption_manager: EncryptionManager, mongo_uri: str, db_name: str, collection_name: str,
                 kms_service: Optional[AWSKMSService] = None):
        self.encryption_manager = encryption_manager
        self.kms_service = kms_service
        self.cache_service = UserKeyCacheService()
        self.client = MongoClient(mongo_uri)
        self.db = self.client[db_name]
        self.collection = self.db[collection_name]

        logger.info(f"UserKeyManager initialized with {'KMS' if kms_service else 'master key'} encryption")

    def create_user_key(self, user_id: str) -> bool:
        """
        Create and store a new user encryption key
        
        Args:
            user_id: User ID
            
        Returns:
            bool: True if created successfully
        """
        try:
            # Check if user already exists
            if self.collection.find_one({"user_id": user_id}):
                logger.info(f"User key already exists for user {user_id}")
                return True
            
            # Generate a 256-bit (32-byte) encryption key
            user_key = secrets.token_bytes(32)
            
            # Encrypt the key (prefer KMS if available)
            if self.kms_service:
                try:
                    encrypted_key_data = self.kms_service.encrypt_user_key(user_key, user_id)
                    encryption_method = "kms"
                    logger.debug(f"Using KMS encryption for user {user_id}")
                except Exception as e:
                    logger.warning(f"KMS encryption failed for user {user_id}: {e}. Using master key.")
                    encrypted_key_data = self.encryption_manager.encrypt_user_key(user_key)
                    encryption_method = "master_key"
            else:
                encrypted_key_data = self.encryption_manager.encrypt_user_key(user_key)
                encryption_method = "master_key"
            
            # Create user document
            current_datetime = datetime.now(pytz.timezone("Asia/Jerusalem")).strftime("%H:%M %d/%m/%Y")
            
            user_key_document = {
                "user_id": user_id,
                "encrypted_key": Binary(encrypted_key_data),
                "encryption_method": encryption_method,
                "created_at": current_datetime,
                "last_accessed": current_datetime
            }
            
            # Store in MongoDB
            self.collection.insert_one(user_key_document)
            logger.info(f"Created user key for user {user_id} using {encryption_method}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create user key for user {user_id}: {e}")
            return False

    async def get_user_key(self, user_id: str) -> bytes:
        """
        Get decrypted user key. Creates one if it doesn't exist.
        
        Args:
            user_id: User ID
            
        Returns:
            bytes: Decrypted user key (32 bytes)
        """
        try:
            # Try to get from cache first
            cached_key = await self.cache_service.get_cached_user_key(user_id)
            if cached_key:
                logger.debug(f"Retrieved user key from cache for user {user_id}")
                return cached_key
            
            # Get from database
            user_data = self.collection.find_one({"user_id": user_id})
            if not user_data:
                # User key doesn't exist, create it
                logger.info(f"No key found for user {user_id}, creating new user key")
                if self.create_user_key(user_id):
                    # Retry getting the user data after creation
                    user_data = self.collection.find_one({"user_id": user_id})
                    if not user_data:
                        raise RuntimeError(f"Failed to create user key for user {user_id}")
                else:
                    raise RuntimeError(f"Failed to create user key for user {user_id}")
            
            # Decrypt the key
            encrypted_key = user_data["encrypted_key"]
            encryption_method = user_data.get("encryption_method", "master_key")
            
            if encryption_method == "kms" and self.kms_service:
                try:
                    decrypted_key = self.kms_service.decrypt_user_key(encrypted_key)
                    logger.debug(f"Decrypted user key using KMS for user {user_id}")
                except Exception as e:
                    logger.warning(f"KMS decryption failed for user {user_id}: {e}. Trying master key.")
                    decrypted_key = self.encryption_manager.decrypt_user_key(encrypted_key)
            else:
                decrypted_key = self.encryption_manager.decrypt_user_key(encrypted_key)
            
            # Cache the decrypted key
            await self.cache_service.cache_user_key(user_id, decrypted_key)
            
            logger.debug(f"Successfully retrieved user key for user {user_id}")
            return decrypted_key
            
        except Exception as e:
            logger.error(f"Failed to get user key for user {user_id}: {e}")
            raise
