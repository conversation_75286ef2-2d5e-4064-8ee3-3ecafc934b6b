"""
Simple UserKeyManager with only 2 essential functions:
1. create_user_key - Creates a new encryption key for a user
2. get_user_key - Gets the decrypted encryption key for a user
"""
import secrets
from typing import Optional
from datetime import datetime
import pytz

from pymongo import MongoClient
from bson.binary import Binary

from chat_history.security.encrypt_manger import EncryptionManager
from chat_history.services.user_key_cache import UserKeyCacheService
from middlewares.logging_utils import app_logger as logger


class UserKeyManager:
    """Simple UserKeyManager with only 2 essential functions"""

    def __init__(self, encryption_manager: EncryptionManager, mongo_uri: str, db_name: str, collection_name: str):
        self.encryption_manager = encryption_manager
        self.cache_service = UserKeyCacheService()
        self.client = MongoClient(mongo_uri)
        self.db = self.client[db_name]
        self.collection = self.db[collection_name]

        logger.info("UserKeyManager initialized with master key encryption")

    def create_user_key(self, user_id: str) -> bool:
        """
        Create a new user in the users collection with encryption key.
        This is called when a user from external platform first uses our system.

        Args:
            user_id: User ID from external platform

        Returns:
            bool: True if created successfully
        """
        try:
            # Check if user already exists in our system
            if self.collection.find_one({"user_id": user_id}):
                logger.info(f"User {user_id} already exists in our system")
                return True

            # Generate a 256-bit (32-byte) encryption key for this user
            user_key = secrets.token_bytes(32)

            # Encrypt the key with master key
            encrypted_key_data = self.encryption_manager.encrypt_user_key(user_key)
            encryption_method = "master_key"

            # Create complete user document for our system
            current_datetime = datetime.now(pytz.timezone("Asia/Jerusalem")).strftime("%H:%M %d/%m/%Y")

            user_document = {
                "user_id": user_id,
                "encrypted_key": Binary(encrypted_key_data),
                "encryption_method": encryption_method,
                "created_at": current_datetime,
                "last_accessed": current_datetime,
                "status": "active",
                "platform": "external",  # Indicates user comes from external platform
                "total_chats": 0,
                "last_chat_date": None
            }

            # Store user in MongoDB users collection
            result = self.collection.insert_one(user_document)
            logger.info(f"Created new user {user_id} in our system with encryption key (ID: {result.inserted_id})")
            return True

        except Exception as e:
            logger.error(f"Failed to create user {user_id} in our system: {e}")
            return False

    async def get_user_key(self, user_id: str) -> bytes:
        """
        Get decrypted user key for existing user or create new user if doesn't exist.
        This handles users coming from external platform.

        Args:
            user_id: User ID from external platform

        Returns:
            bytes: Decrypted user key (32 bytes)
        """
        try:
            # Try to get from cache first
            cached_key = await self.cache_service.get_cached_user_key(user_id)
            if cached_key:
                # Update last accessed time for existing user
                self._update_user_last_accessed(user_id)
                logger.debug(f"Retrieved user key from cache for user {user_id}")
                return cached_key

            # Get user from database
            user_data = self.collection.find_one({"user_id": user_id})
            if not user_data:
                # User doesn't exist in our system - create them
                logger.info(f"User {user_id} not found in our system, creating new user from external platform")
                if self.create_user_key(user_id):
                    # Retry getting the user data after creation
                    user_data = self.collection.find_one({"user_id": user_id})
                    if not user_data:
                        raise RuntimeError(f"Failed to create user {user_id} in our system")
                else:
                    raise RuntimeError(f"Failed to create user {user_id} in our system")

            # Decrypt the user's encryption key
            encrypted_key = user_data["encrypted_key"]
            decrypted_key = self.encryption_manager.decrypt_user_key(encrypted_key)

            # Update user's last accessed time
            self._update_user_last_accessed(user_id)

            # Cache the decrypted key for performance
            await self.cache_service.cache_user_key(user_id, decrypted_key)

            logger.debug(f"Successfully retrieved user key for user {user_id}")
            return decrypted_key

        except Exception as e:
            logger.error(f"Failed to get user key for user {user_id}: {e}")
            raise

    def _update_user_last_accessed(self, user_id: str):
        """Update user's last accessed time"""
        try:
            current_datetime = datetime.now(pytz.timezone("Asia/Jerusalem")).strftime("%H:%M %d/%m/%Y")
            self.collection.update_one(
                {"user_id": user_id},
                {"$set": {"last_accessed": current_datetime}}
            )
        except Exception as e:
            logger.warning(f"Failed to update last accessed time for user {user_id}: {e}")

    def update_user_chat_stats(self, user_id: str):
        """Update user's chat statistics when they store a new chat"""
        try:
            current_datetime = datetime.now(pytz.timezone("Asia/Jerusalem")).strftime("%H:%M %d/%m/%Y")
            self.collection.update_one(
                {"user_id": user_id},
                {
                    "$inc": {"total_chats": 1},
                    "$set": {
                        "last_chat_date": current_datetime,
                        "last_accessed": current_datetime
                    }
                }
            )
            logger.debug(f"Updated chat statistics for user {user_id}")
        except Exception as e:
            logger.warning(f"Failed to update chat statistics for user {user_id}: {e}")
