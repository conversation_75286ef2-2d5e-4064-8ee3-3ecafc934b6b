import base64
import traceback
from typing import Optional

from pymongo import MongoClient
from pymongo.errors import PyMongoError

from chat_history.security.encrypt_manger import EncryptionManager
from chat_history.services.aws_kms_service import AWSKMSService
from chat_history.services.user_key_cache import UserKeyCacheService
from chat_history.decorators import audit_store, audit_retrieve, audit_log
from chat_history.schema.audit_models import AuditActionEnum
from bson.binary import Binary
from middlewares.logging_utils import app_logger as logger

from datetime import datetime
import pytz


class UserKeyManager:

    def __init__(self, encryption_manager: EncryptionManager, mongo_uri: str, db_name: str, collection_name: str,
                 kms_service: Optional[AWSKMSService] = None):
        self.encryption_manager = encryption_manager
        self.kms_service = kms_service
        self.cache_service = UserKeyCacheService()
        self.client = MongoClient(mongo_uri)
        self.db = self.client[db_name]
        self.collection = self.db[collection_name]

        # Log initialization status
        if self.kms_service:
            logger.info("UserKeyManager initialized with KMS support")
        else:
            logger.info("UserKeyManager initialized with master key encryption only")

    @audit_store("UserKeyManager", user_id_param="user_id")
    def create_user_key(self, user_id: str):
        """
        Create a new user key with KMS encryption
        
        Args:
            user_id: User ID
            
        Returns:
            Binary: KMS-encrypted user key
        """
        # Check if user key already exists
        existing_user = self.collection.find_one({"user_id": user_id})
        if existing_user:
            logger.info(f"User key already exists for user {user_id}")
            return existing_user.get("kms_encrypted_user_key", existing_user.get("encrypted_user_key"))

        # Generate new user key
        user_key = self.encryption_manager.generate_user_key()
        logger.info(f"Generated new user key for user {user_id}")
        
        # Encrypt user key with KMS if available
        if self.kms_service:
            kms_encrypted_key = self.kms_service.encrypt_user_key(user_key, user_id)
            encrypted_field_name = "kms_encrypted_user_key"
            encrypted_key_data = Binary(kms_encrypted_key)
            encryption_method = "AWS_KMS"
        else:
            # Fallback to master key encryption
            logger.warning(f"KMS service not available, using master key encryption for user {user_id}")
            master_encrypted_key = self.encryption_manager.encrypt(user_key)
            encrypted_field_name = "encrypted_user_key"
            encrypted_key_data = Binary(master_encrypted_key)
            encryption_method = "MASTER_KEY"

        current_datetime = datetime.now(pytz.timezone("Asia/Jerusalem")).strftime("%H:%M %d/%m/%Y")

        # Store encrypted user key in MongoDB
        user_key_document = {
            "user_id": user_id,
            encrypted_field_name: encrypted_key_data,
            "encryption_method": encryption_method,
            "create_date": current_datetime,
            "last_accessed": current_datetime
        }
        
        result = self.collection.insert_one(user_key_document)
        logger.info(f"Successfully created and stored user key for user {user_id} using {encryption_method}")
        return encrypted_key_data

    @audit_retrieve("UserKeyManager", user_id_param="user_id")
    async def get_user_key(self, user_id: str) -> bytes:
        """
        Get decrypted user key with caching and KMS support
        
        Args:
            user_id: User ID
            
        Returns:
            bytes: Decrypted user key
        """
        # Try to get from cache first
        cached_key = await self.cache_service.get_cached_user_key(user_id)
        if cached_key:
            logger.debug(f"Retrieved user key from cache for user {user_id}")
            return cached_key
        
        # Get encrypted key from MongoDB
        user_data = self.collection.find_one({"user_id": user_id})
        if not user_data:
            error_message = f"No key found for user {user_id}"
            logger.error(error_message)
            raise ValueError(error_message)
        
        # Update last accessed time
        current_datetime = datetime.now(pytz.timezone("Asia/Jerusalem")).strftime("%H:%M %d/%m/%Y")
        self.collection.update_one(
            {"user_id": user_id},
            {"$set": {"last_accessed": current_datetime}}
        )
        
        # Decrypt user key based on encryption method
        encryption_method = user_data.get("encryption_method", "MASTER_KEY")
        
        if encryption_method == "AWS_KMS" and self.kms_service:
            # Decrypt using KMS
            encrypted_key = user_data["kms_encrypted_user_key"]
            decrypted_key = self.kms_service.decrypt_user_key(encrypted_key, user_id)
        else:
            # Fallback to master key decryption
            if "encrypted_user_key" in user_data:
                encrypted_key = user_data["encrypted_user_key"]
                decrypted_key = self.encryption_manager.decrypt(encrypted_key)
            elif "kms_encrypted_user_key" in user_data:
                # KMS encrypted but no KMS service available
                error_message = f"User key is KMS encrypted but KMS service not available for user {user_id}"
                logger.error(error_message)
                raise RuntimeError(error_message)
            else:
                error_message = f"No valid encrypted key found for user {user_id}"
                logger.error(error_message)
                raise ValueError(error_message)
        
        # Cache the decrypted key
        await self.cache_service.cache_user_key(user_id, decrypted_key)
        
        logger.debug(f"Successfully retrieved and decrypted user key for user {user_id}")
        return decrypted_key

    def get_user_key_id(self, user_id: str) -> str:
        """
        Get user key ID for audit logging purposes
        
        Args:
            user_id: User ID
            
        Returns:
            str: User key ID (ObjectId as string)
        """
        user_data = self.collection.find_one({"user_id": user_id})
        if not user_data:
            raise ValueError(f"No key found for user {user_id}.")
        
        return str(user_data["_id"])

    async def invalidate_user_key_cache(self, user_id: str) -> bool:
        """
        Invalidate cached user key
        
        Args:
            user_id: User ID
            
        Returns:
            bool: True if successfully invalidated
        """
        return await self.cache_service.invalidate_user_key(user_id)

    def get_user_encryption_method(self, user_id: str) -> Optional[str]:
        """
        Get the encryption method used for a user's key
        
        Args:
            user_id: User ID
            
        Returns:
            Optional[str]: Encryption method or None if user not found
        """
        try:
            user_data = self.collection.find_one({"user_id": user_id})
            if not user_data:
                return None
            return user_data.get("encryption_method", "MASTER_KEY")
        except Exception as e:
            logger.error(f"Error getting encryption method for user {user_id}: {e}")
            return None

    @audit_log("UserKeyManager", "migrate_to_kms", user_id_param="user_id", details_template="Migrated user key to KMS encryption")
    async def migrate_user_to_kms(self, user_id: str) -> bool:
        """
        Migrate a user's key from master key encryption to KMS encryption

        Args:
            user_id: User ID

        Returns:
            bool: True if successfully migrated
        """
        if not self.kms_service:
            logger.error("KMS service not available for migration")
            raise RuntimeError("KMS service not available for migration")

        user_data = self.collection.find_one({"user_id": user_id})
        if not user_data:
            logger.error(f"User {user_id} not found for migration")
            raise ValueError(f"User {user_id} not found for migration")

        encryption_method = user_data.get("encryption_method", "MASTER_KEY")
        if encryption_method == "AWS_KMS":
            logger.info(f"User {user_id} already using KMS encryption")
            return True

        # Decrypt with master key
        if "encrypted_user_key" not in user_data:
            logger.error(f"No master key encrypted data found for user {user_id}")
            raise ValueError(f"No master key encrypted data found for user {user_id}")

        decrypted_key = self.encryption_manager.decrypt(user_data["encrypted_user_key"])

        # Encrypt with KMS
        kms_encrypted_key = self.kms_service.encrypt_user_key(decrypted_key, user_id)

        # Update database
        current_datetime = datetime.now(pytz.timezone("Asia/Jerusalem")).strftime("%H:%M %d/%m/%Y")

        result = self.collection.update_one(
            {"user_id": user_id},
            {
                "$set": {
                    "kms_encrypted_user_key": Binary(kms_encrypted_key),
                    "encryption_method": "AWS_KMS",
                    "migrated_to_kms": current_datetime,
                    "last_accessed": current_datetime
                },
                "$unset": {
                    "encrypted_user_key": ""
                }
            }
        )

        if result.modified_count > 0:
            # Invalidate cache
            await self.cache_service.invalidate_user_key(user_id)
            logger.info(f"Successfully migrated user {user_id} to KMS encryption")
            return True
        else:
            logger.error(f"Failed to update database for user {user_id} migration")
            raise RuntimeError(f"Failed to update database for user {user_id} migration")
