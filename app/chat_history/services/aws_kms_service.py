import boto3
import traceback
from typing import Optional
from botocore.exceptions import Client<PERSON>rror, BotoCoreError

from chat_history.decorators import audit_encrypt, audit_decrypt
from middlewares.logging_utils import app_logger as logger
from configs.app_config import S3_Region


class AWSKMSService:
    """
    AWS KMS service for encrypting and decrypting user keys
    """
    
    def __init__(self, kms_key_id: str, region: str = S3_Region):
        """
        Initialize AWS KMS service
        
        Args:
            kms_key_id: AWS KMS key ID or ARN
            region: AWS region
        """
        self.kms_key_id = kms_key_id
        self.region = region
        self._kms_client = None
    
    @property
    def kms_client(self):
        """Lazy initialization of KMS client"""
        if self._kms_client is None:
            try:
                self._kms_client = boto3.client('kms', region_name=self.region)
                logger.info(f"AWS KMS client initialized for region {self.region}")
            except Exception as e:
                logger.error(f"Failed to initialize AWS KMS client: {e}")
                raise RuntimeError(f"Failed to initialize AWS KMS client: {e}")
        return self._kms_client
    

