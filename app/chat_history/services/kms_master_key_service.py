"""
KMS Master Key Service - Simple service to retrieve master key from AWS KMS
"""
import os
import boto3
from botocore.exceptions import ClientError, BotoCoreError
import configs.app_config as conf
from middlewares.logging_utils import app_logger as logger


class KMSMasterKeyService:
    """Simple service to retrieve master key from AWS KMS"""
    
    @staticmethod
    async def get_master_key() -> bytes:
        """
        Get master key from AWS KMS or fallback to environment variable
        
        Returns:
            bytes: 32-byte master key for EncryptionManager
        """
        try:
            # Check if K<PERSON> is configured
            if not hasattr(conf, 'AWS_KMS_KEY_ID') or not conf.AWS_KMS_KEY_ID:
                logger.warning("AWS KMS not configured, using environment variable")
                return KMSMasterKeyService._get_fallback_key()
            
            # Try to get from KMS
            return await KMSMasterKeyService._get_from_kms()
            
        except Exception as e:
            logger.error(f"Failed to get master key from KMS: {e}")
            logger.warning("Using fallback master key")
            return KMSMasterKeyService._get_fallback_key()
    
    @staticmethod
    async def _get_from_kms() -> bytes:
        """Get master key from AWS KMS"""
        try:
            kms_client = boto3.client('kms', region_name=conf.AWS_KMS_REGION)
            
            # Check if we have an encrypted master key in environment
            encrypted_key_b64 = os.environ.get('ENCRYPTED_MASTER_KEY')
            if encrypted_key_b64:
                return await KMSMasterKeyService._decrypt_from_env(kms_client, encrypted_key_b64)
            else:
                return await KMSMasterKeyService._generate_new_key(kms_client)
                
        except (ClientError, BotoCoreError) as e:
            logger.error(f"AWS KMS error: {e}")
            raise
    
    @staticmethod
    async def _decrypt_from_env(kms_client, encrypted_key_b64: str) -> bytes:
        """Decrypt master key from environment variable"""
        import base64
        
        encrypted_key = base64.b64decode(encrypted_key_b64)
        response = kms_client.decrypt(CiphertextBlob=encrypted_key)
        
        logger.info("Successfully decrypted master key from environment using KMS")
        return response['Plaintext']
    
    @staticmethod
    async def _generate_new_key(kms_client) -> bytes:
        """Generate new master key using KMS"""
        response = kms_client.generate_data_key(
            KeyId=conf.AWS_KMS_KEY_ID,
            KeySpec='AES_256'
        )
        
        # Log encrypted version for future use
        import base64
        encrypted_key_b64 = base64.b64encode(response['CiphertextBlob']).decode()
        logger.info(f"Generated new master key. Store this in ENCRYPTED_MASTER_KEY: {encrypted_key_b64[:50]}...")
        
        return response['Plaintext']
    
    @staticmethod
    def _get_fallback_key() -> bytes:
        """Get fallback master key from environment"""
        key = os.environ.get('KMS_MASTER_KEY', 'default_32_byte_key_for_testing_').encode()[:32]
        if len(key) < 32:
            key = key.ljust(32, b'0')
        return key
