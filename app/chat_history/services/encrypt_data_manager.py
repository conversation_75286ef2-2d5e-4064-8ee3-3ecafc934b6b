import traceback
from enum import Enum

from pydantic import BaseModel
from pymongo.errors import PyMongoError
from pymongo.results import InsertOneResult

from chat_history.schema.mongo_models import MongoChatSchema
from chat_history.decorators import audit_store, audit_retrieve, audit_log, audit_encrypt, audit_decrypt
from chat_history.schema.audit_models import AuditActionEnum
from chat_history.security.encrypt_manger import EncryptionManager
from chat_history.services.user_key_manager import UserKeyManager
from configs.app_config import MON<PERSON>O_DB_HISTORY, MONGO_CHAT_HISTORY_COLLECTION
from redis_db.redis_schema import RedisSchema
from utils.datetime_utils import timestamp_now_il
from middlewares.logging_utils import app_logger as logger

from typing import Any, List


@audit_encrypt("ConversationEncryption", user_id_param="user_id", user_key_id_param="user_key_id")
def encrypt_conversation_data(
    conversation: List[Any],
    user_key: bytes,
    encryption_manager: Any,
    user_id: str,
    chat_id: str,
    user_key_id: str = None,
    background_tasks: Any = None
) -> tuple[bytes, str]:
    """
    Encrypt conversation data and generate hash

    Args:
        conversation: Conversation data to encrypt
        user_key: User's encryption key
        encryption_manager: Encryption manager instance
        user_id: User ID for audit
        chat_id: Chat ID for audit
        user_key_id: User key ID for audit
        background_tasks: FastAPI BackgroundTasks for audit logging

    Returns:
        Tuple of (encrypted_conversation, conversation_hash)
    """
    # Generate conversation hash
    conversation_hash = encryption_manager.generate_conversation_hash(conversation)

    # Encrypt conversation using user key
    encrypted_conversation = encryption_manager.encrypt_conversation_with_user_key(
        conversation, user_key
    )

    logger.info(f"Encrypted conversation for chat {chat_id} with {len(conversation)} items")
    return encrypted_conversation, conversation_hash


def convert_redis_to_mongo(redis_obj: RedisSchema, encryption_manager: Any = None, user_key_id: str = None, user_key: bytes = None, background_tasks: Any = None) -> MongoChatSchema:
    """
    Convert RedisSchema to MongoChatSchema with optional encryption.

    :param redis_obj: RedisSchema object.
    :param encryption_manager: EncryptionManager instance for encrypting conversation
    :param user_key_id: User key ID for audit logging
    :param user_key: User's decrypted key for conversation encryption
    :param background_tasks: FastAPI BackgroundTasks for audit logging
    :return: MongoChatSchema object.
    """
    ##by jerusalem_time_string
    redis_obj.chat_settings.update_time = timestamp_now_il()

    # Initialize fields
    encrypted_conversation = None
    conversation_hash = None
    conversation = redis_obj.conversation  # Keep for backward compatibility

    # Encrypt conversation if encryption manager and user key are provided
    if encryption_manager and redis_obj.conversation and user_key:
        encrypted_conversation, conversation_hash = encrypt_conversation_data(
            conversation=redis_obj.conversation,
            user_key=user_key,
            encryption_manager=encryption_manager,
            user_id=redis_obj.user_id,
            chat_id=redis_obj.chat_id,
            user_key_id=user_key_id,
            background_tasks=background_tasks
        )

    return MongoChatSchema(
        user_id=redis_obj.user_id,
        chat_id=redis_obj.chat_id,
        domain=redis_obj.domain,
        encrypted_conversation=encrypted_conversation,
        conversation_hash=conversation_hash,
        conversation=conversation,
        summaries=redis_obj.summaries,
        model_settings=redis_obj.model_settings,
        chat_settings=redis_obj.chat_settings,
        filters=redis_obj.filters or {},
        citations=redis_obj.citations or [],
        # Add schema versioning and premium user fields
        schema_version="1.0",
    )


@audit_decrypt("ConversationDecryption", user_id_param="user_id", user_key_id_param="user_key_id")
def decrypt_conversation_data(
    encrypted_conversation: bytes,
    conversation_hash: str,
    user_key: bytes,
    encryption_manager: Any,
    user_id: str,
    chat_id: str,
    user_key_id: str = None,
    background_tasks: Any = None
) -> List[Any]:
    """
    Decrypt conversation data and verify hash

    Args:
        encrypted_conversation: Encrypted conversation bytes
        conversation_hash: Hash for verification
        user_key: User's decryption key
        encryption_manager: Encryption manager instance
        user_id: User ID for audit
        chat_id: Chat ID for audit
        user_key_id: User key ID for audit
        background_tasks: FastAPI BackgroundTasks for audit logging

    Returns:
        Decrypted conversation list
    """
    # Decrypt conversation using user key
    decrypted_conversation = encryption_manager.decrypt_conversation_with_user_key(
        encrypted_conversation, user_key
    )

    # Verify hash if available
    if conversation_hash:
        hash_valid = encryption_manager.verify_conversation_hash(
            decrypted_conversation,
            conversation_hash
        )

        if not hash_valid:
            logger.error(f"Hash verification failed for chat {chat_id}")
            raise ValueError(f"Hash verification failed for chat {chat_id}")

    logger.info(f"Decrypted conversation for chat {chat_id} with {len(decrypted_conversation)} items")
    return decrypted_conversation


def convert_mongo_to_redis(mongo_obj: MongoChatSchema, encryption_manager: Any = None, user_key_id: str = None, user_key: bytes = None, background_tasks: Any = None) -> RedisSchema:
    """
    Convert MongoChatSchema to RedisSchema with optional decryption.

    :param mongo_obj: MongoChatSchema object.
    :param encryption_manager: EncryptionManager instance for decrypting conversation
    :param user_key_id: User key ID for audit logging
    :param user_key: User's decrypted key for conversation decryption
    :param background_tasks: FastAPI BackgroundTasks for audit logging
    :return: RedisSchema object.
    """
    conversation = mongo_obj.conversation or []  # Default to plain conversation

    # Decrypt conversation if encrypted data and user key exist
    if encryption_manager and mongo_obj.encrypted_conversation and user_key:
        conversation = decrypt_conversation_data(
            encrypted_conversation=mongo_obj.encrypted_conversation,
            conversation_hash=mongo_obj.conversation_hash,
            user_key=user_key,
            encryption_manager=encryption_manager,
            user_id=mongo_obj.user_id,
            chat_id=mongo_obj.chat_id,
            user_key_id=user_key_id,
            background_tasks=background_tasks
        )

    return RedisSchema(
        user_id=mongo_obj.user_id,
        chat_id=mongo_obj.chat_id,
        domain=mongo_obj.domain,
        conversation=conversation,
        summaries=mongo_obj.summaries,
        model_settings=mongo_obj.model_settings,
        chat_settings=mongo_obj.chat_settings,
        filters=mongo_obj.filters or {}
    )


def fix_enums(obj):
    """
    Recursively converts Enum members to their values,
    and datetime/date objects to ISO 8601 strings.
    Converts Pydantic BaseModel instances to dictionaries.
    """
    if isinstance(obj, BaseModel):
        # Pydantic's .model_dump() already handles aliases if configured on the model.
        # Calling model_dump() here ensures we get a plain Python dict.
        return fix_enums(obj.model_dump())
    elif isinstance(obj, dict):
        return {k: fix_enums(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [fix_enums(i) for i in obj]
    elif isinstance(obj, Enum):
        return obj.value
    else:
        return obj


class ChatHistoryManager:

    def __init__(self, encryption_manager: EncryptionManager, mongo_client: Any, user_key_manager: UserKeyManager = None):
        if encryption_manager is None:
            raise ValueError("EncryptionManager is required and cannot be None")
        self.encryption_manager = encryption_manager
        self.user_key_manager = user_key_manager
        self.client = mongo_client
        self.collection = self.client[MONGO_DB_HISTORY][MONGO_CHAT_HISTORY_COLLECTION]

    # @audit_store("ChatHistoryManager", user_id_param="redis_chat_data.user_id", chat_id_param="redis_chat_data.chat_id")
    async def store_chat(self, redis_chat_data: RedisSchema) -> InsertOneResult:
        # Get user key ID for audit logging and user key for encryption
        user_key_id = None
        user_key = None
        
        if self.user_key_manager:
            try:
                user_key, user_key_id = await self.user_key_manager.get_user_key_and_id(redis_chat_data.user_id)
            except Exception as e:
                logger.warning(f"Could not get user key for encryption: {e}")
                # Continue without user-specific encryption

        # Convert with encryption using user key
        mongo_chat = convert_redis_to_mongo(
            redis_chat_data, 
            encryption_manager=self.encryption_manager,
            user_key_id=user_key_id,
            user_key=user_key
        )
        mongo_chat = fix_enums(mongo_chat)

        object_mongo = await self.collection.update_one(
            {"chat_id": mongo_chat["chat_id"]},
            {"$set": mongo_chat},
            upsert=True
        )

        return object_mongo

    @audit_retrieve("ChatHistoryManager", user_id_param="user_id", chat_id_param="chat_id")
    async def extract_chat_history(self, chat_id: str, user_id: str = None) -> RedisSchema | None:
        client_data = await self.collection.find_one({"chat_id": chat_id})

        if not client_data:
            return None
            
        mongo_chat = MongoChatSchema.model_validate(client_data)
        
        # Use provided user_id or extract from mongo_chat
        if not user_id:
            user_id = mongo_chat.user_id

        # Get user key ID for audit logging and user key for decryption
        user_key_id = None
        user_key = None
        
        if self.user_key_manager:
            try:
                user_key, user_key_id = await self.user_key_manager.get_user_key_and_id(user_id)
            except Exception as e:
                logger.warning(f"Could not get user key for decryption: {e}")
                # Continue without user-specific decryption

        # Convert with decryption using user key
        redis_chat = convert_mongo_to_redis(
            mongo_chat,
            encryption_manager=self.encryption_manager,
            user_key_id=user_key_id,
            user_key=user_key
        )

        return redis_chat
