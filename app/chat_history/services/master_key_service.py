"""
Master Key Service - Simple service to retrieve master key from environment
"""
import os
from middlewares.logging_utils import app_logger as logger


class MasterKeyService:
    """Simple service to retrieve master key from environment"""
    
    @staticmethod
    async def get_master_key() -> bytes:
        """
        Get master key from environment variable
        
        Returns:
            bytes: 32-byte master key for EncryptionManager
        """
        try:
            # Get master key from environment
            master_key = os.environ.get('MASTER_KEY', 'default_32_byte_key_for_testing_').encode()[:32]
            if len(master_key) < 32:
                master_key = master_key.ljust(32, b'0')  # Pad to 32 bytes if needed
            
            logger.info("Master key retrieved from environment variable")
            return master_key
            
        except Exception as e:
            logger.error(f"Failed to get master key: {e}")
            # Use default fallback
            fallback_key = b'default_32_byte_key_for_testing_'[:32]
            if len(fallback_key) < 32:
                fallback_key = fallback_key.ljust(32, b'0')
            logger.warning("Using default fallback master key")
            return fallback_key
