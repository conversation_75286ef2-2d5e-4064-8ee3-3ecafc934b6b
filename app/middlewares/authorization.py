"""
Legacy Authorization Module

Now simplified - middleware handles all authentication automatically.
These functions are kept for backward compatibility only.
"""

import json
from enum import Enum
import requests
from typing import Dict, Any, Tuple

import configs.app_config as conf
from middlewares.trace_utils import request_token_ctx
from utils.dto import ServiceTypeEnum, ProductNameEnum
from middlewares.logging_utils import app_logger as logger
from fastapi.security import API<PERSON>ey<PERSON>eader
from fastapi import HTTP<PERSON>xception, Security


# API Key configuration
API_KEY = conf.ETL_API
api_key_header = APIKeyHeader(name="apikey")
config_settings = conf.AppConfig()


def get_api_key(api_key: str = Security(api_key_header)) -> str:
    """Validate API key for ETL endpoints"""
    if api_key != API_KEY:
        raise HTTPException(status_code=400, detail="Invalid API Key")
    return api_key


class Premission(str, Enum):
    """Legacy permission enum - kept for backward compatibility"""
    ethics_chat = "ethic-chat"
    ethics_search = "ethic-search"
    ethics_read = "ethic-read"
    verdict_chat = "verdict-chat"
    verdict_search = "verdict-search"
    law_chat = "law-chat"
    law_search = "law-search"
    law_read = "law-read"
    machshavot_chat = "machshavot-chat"
    machshavot_search = "machshavot-search"
    machshavot_read = "machshavot-read"
    machshavot_trainer = "machshavot-trainer"

    @classmethod
    def from_key(cls, key_name):
        member = cls.__members__.get(key_name, None)
        return member.value if member else None


def create_headers_with_jwt():
    """Legacy function - creates headers with JWT token"""
    token = request_token_ctx.get()
    return {
        "Content-Type": 'application/json',
        "Authorization": token,
    }


def jwt_token_valid(payload):
    """Legacy JWT validation - kept for backward compatibility"""
    try:
        http_headers = create_headers_with_jwt()
        if not http_headers['Authorization']:
            return False, 'על מנת להנות משירותי המערכת, לשאול שאלות, לבצע חיפושים חכמים ועוד, יש להזין פרטי מנוי', 401

        response = requests.post(
            url=conf.VALIDATE_TOKEN_URL_TEMPLATE,
            json=payload,
            headers=http_headers,
            verify=False
        )

        if response.status_code in [200, 403]:
            response_json = json.loads(response.content.decode('utf-8'))
            allow = response_json.get('allow')
            message = response_json.get('message')
            statusCode = response_json.get('statusCode')
            return allow, message, statusCode
        elif response.status_code == 401:
            return False, 'על מנת להנות משירותי המערכת, לשאול שאלות, לבצע חיפושים חכמים ועוד, יש להזין פרטי מנוי', 401
        elif response.status_code == 403:
            return False, 'לא ניתן לבצע בדיקת הרשאה', 403
        else:
            return False, 'לא ניתן לבצע בדיקת הרשאה יש לבצע התחברות מחודשת', 444
    except Exception as e:
        return False, 'לא ניתן לבצע בדיקת הרשאה יש לבצע התחברות מחודשת', 444


def choose_product(request_dto, request_fastapi) -> Tuple[bool, Dict[str, Any]]:
    """Legacy product selection - kept for backward compatibility"""
    auth_exist = False
    route_name = request_fastapi.url.path.strip("/").split("/")[-1]

    try:
        body = json.loads(request_dto.json())
    except:
        body = {}

    if route_name in {"pre-chat", "chat", "post-chat"}:
        domain = body.get("domain", "")
        required_permission_template = f"{domain}_{ServiceTypeEnum.chat.value}"
        auth_exist = True
    elif route_name in {"search"}:
        type_search = [con.get("value") for con in body.get("filters", []) if con.get("column") == "type"]
        if type_search:
            required_permission_template = f"{type_search[0]}_{ServiceTypeEnum.search.value}"
            auth_exist = True

    if auth_exist:
        try:
            required_permission = Premission.from_key(required_permission_template)
            if not required_permission:
                auth_exist = False
        except Exception as e:
            logger.error(f"Error in choose_product: {required_permission_template}, {e}")
            auth_exist = False

    return auth_exist, {
        "name": required_permission if auth_exist else "",
        "type": int(ProductNameEnum.techdin.value),
        "usage": False
    }


def handle_request_product(request_dto, request_fastapi) -> Tuple[bool, str, int]:
    """Legacy request handling - kept for backward compatibility"""
    auth_exist, payload = choose_product(request_dto, request_fastapi)
    if not auth_exist:
        return False, 'לא ניתן לבצע בדיקת הרשאה', 403

    if config_settings.exclude_items and payload.get("name", "") in config_settings.exclude_items:
        return True, '', 200

    allow, message, statusCode = jwt_token_valid(payload=payload)
    logger.info(f"Allow: {allow}, Message: {message}, Status Code: {statusCode}")
    return allow, message, statusCode
