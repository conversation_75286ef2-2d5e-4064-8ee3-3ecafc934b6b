"""
Simple FastAPI Authentication Middleware

Processes every request with <PERSON>W<PERSON> claims validation.
"""

import json
import jwt
import requests
import os
from typing import Dict, Any, Optional, Coroutine

from fastapi import HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response, JSONResponse

from configs.app_config import VALIDATE_TOKEN_URL_TEMPLATE
from middlewares.authorization import Premission
from middlewares.trace_utils import request_token_ctx, endpoint_name_ctx
from middlewares.logging_utils import app_logger as logger
from utils.dto import ProductNameEnum, ServiceTypeEnum


def _extract_token(request: Request) -> Optional[str]:
    """Extract JWT token from request headers"""
    return (
            request.headers.get("authorization") or
            request.headers.get("Authorization") or
            request.headers.get("token")
    )


def _parse_jwt_token(token: str) -> Dict[str, Any]:
    """Parse JWT token to extract user information"""
    try:
        # Remove Bearer prefix if present
        if token.startswith("Bearer "):
            token = token[7:]

        # Decode JWT token (without verification for now)
        decoded_token = jwt.decode(token, options={"verify_signature": False})

        user_id = decoded_token.get("sub") or decoded_token.get("user_id")
        if not user_id:
            raise ValueError("User ID not found in token")

        return {
            "user_id": user_id,
            "claims": decoded_token.get("claims", {}),
            "permissions": decoded_token.get("permissions", []),
            "token": f"Bearer {token}"
        }

    except Exception as e:
        logger.error(f"Error parsing JWT token: {e}")
        raise ValueError("Invalid token format")


async def _determine_permission(request: Request) -> bool | dict[str, None | int | bool | Any]:
    """Determine required permission based on route"""
    if request.method in ["GET"]:
        return False
    try:
        # Extract body from request
        body = await request.json()
    except Exception as e:
        logger.warning(f"Could not parse request body: {e}")
        body = {}
    required_permission = None

    path_parts = request.url.path.strip("/").split("/")
    route_name = path_parts[-1] if path_parts else ""

    auth_exist = False
    required_permission_template = ""

    if route_name in {"pre-chat", "chat", "post-chat"}:
        domain = body.get("domain", "")
        required_permission_template = f"{domain}_{ServiceTypeEnum.chat.value}"
        auth_exist = True
    elif route_name in {"search"}:
        type_search = [con.get("value") for con in body.get("filters", []) if con.get("column") == "type"]
        if type_search:
            required_permission_template = f"{type_search[0]}_{ServiceTypeEnum.search.value}"
            auth_exist = True

    if auth_exist:
        try:
            required_permission = Premission.from_key(required_permission_template)
            if not required_permission:
                auth_exist = False
        except Exception as e:
            logger.error(f"Error in choose_product: {required_permission_template}")
            auth_exist = False

    return {"name": required_permission, "type": int(ProductNameEnum.techdin.value), "usage": False}


async def _validate_with_idp(token: str, permission: dict) -> bool:
    """Validate permission with IDP server"""
    try:
        headers = {
            "Content-Type": "application/json",
            "Authorization": token,
        }

        response = requests.post(
            url=VALIDATE_TOKEN_URL_TEMPLATE,
            json=permission,
            headers=headers,
            verify=False
        )

        if response.status_code in [200, 403]:
            response_json = response.json()
            return response_json.get('allow', False)

        return False

    except Exception as e:
        logger.error(f"IDP validation error: {e}")
        return False


class AuthMiddleware(BaseHTTPMiddleware):
    """Simple authentication middleware with JWT claims validation"""

    def __init__(self, app):
        super().__init__(app)

        # Configuration from environment
        self.enabled = os.getenv("AUTH_MIDDLEWARE_ENABLED", "true").lower() == "true"
        # self.idp_url = os.getenv("IDP_VALIDATION_URL",
        #                          'https://web01-dev-devops-hashavim-co-il.azurewebsites.net/api/user-info')

        # Routes that don't require authentication
        self.public_routes = {"/docs", "/redoc", "/openapi.json", "/health", "/ping", "/favicon.ico"}
        self.body = None  # Store request body for debugging
        # Error messages
        self.error_messages = {
            "no_token": "על מנת להנות משירותי המערכת, לשאול שאלות, לבצע חיפושים חכמים ועוד, יש להזין פרטי מנוי",
            "invalid_token": "על המשתמש להזדהות מחדש",
            "permission_denied": "לא ניתן לבצע בדיקת הרשאה",
            "reconnect_required": "לא ניתן לבצע בדיקת הרשאה יש לבצע התחברות מחודשת"
        }

    def valid_package(self, permission, token: str) -> Dict[str, Any]:
        """Check if the permission is valid for the current package"""
        try:
            http_headers = {
                "Content-Type": "application/json",
                "Authorization": token,
            }

            response = requests.post(url=VALIDATE_TOKEN_URL_TEMPLATE, json=permission, headers=http_headers,
                                     verify=False)
            if response.status_code in [200, 403]:
                response_json = response.json()
                allow = response_json.get('allow', False)
                message = response_json.get('message', 'Permission granted')
                status_code = response_json.get('statusCode', 200)

                if not allow:
                    return {"statusCode": status_code, "message": message}

                return {"statusCode": 200, "message": "Permission granted"}
            elif response.status_code == 401:
                return {"statusCode": 401, "message": self.error_messages["no_token"]}
            elif response.status_code == 403:
                return {"statusCode": 403, "message": self.error_messages["permission_denied"]}
            else:
                return {"statusCode": 444, "message": self.error_messages["reconnect_required"]}
        except Exception as e:
            logger.error(f"Error validating permission with IDP: {e}")
            return {"statusCode": 444, "message": self.error_messages["reconnect_required"]}

    async def authenticate_request(self, request: Request):
        """Reusable authentication logic for routers via Depends"""

        # Set endpoint context
        endpoint_name_ctx.set(request.url.path)

        try:
            token = _extract_token(request)
            if not token:
                raise HTTPException(status_code=401, detail="Missing token")

            user_info = _parse_jwt_token(token)

            if request.method in ["POST", "PUT", "DELETE", "GET"]:
                body_user_id = await self._extract_user_id_from_body(request)
                if body_user_id and body_user_id != user_info["user_id"]:
                    raise HTTPException(status_code=403, detail="Access denied")

            permission = await _determine_permission(request)
            if permission:
                vp = self.valid_package(permission, token)
                if vp["statusCode"] != 200:
                    raise HTTPException(status_code=vp["statusCode"], detail=vp["message"])

            # Set request.state and context
            request.state.user_id = user_info["user_id"]
            request.state.user_claims = user_info.get("claims", {})
            request.state.user_permissions = user_info.get("permissions", [])
            request_token_ctx.set(token)
            request._receive = {"type": "http.request", "body": self.body}  # Store body for debugging


        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=401, detail="Invalid token")

    async def dispatch(self, request: Request, call_next) -> Response:
        """Process request with authentication"""
        # Skip if disabled
        if not self.enabled:
            return await call_next(request)

        # Skip OPTIONS requests
        if request.method == "OPTIONS":
            return await call_next(request)

        # Skip public routes
        if any(public in request.url.path for public in self.public_routes):
            return await call_next(request)

        try:

            ar = await self.authenticate_request(request)
            if isinstance(ar, HTTPException):
                raise ar
        except HTTPException as e:
            logger.error(f"Authentication error: {e.detail}")
            return JSONResponse(
                content={"message": e.detail},
                status_code=e.status_code
            )
        # Uncomment this block if you want to use the IDP validation

        # Set token in context for legacy functions

        # Continue to endpoint

        response = await call_next(request)
        return response

        # try:
        #     # Extract token
        #     token = _extract_token(request)
        #     if not token:
        #         return JSONResponse(
        #             content={"message": self.error_messages["no_token"]},
        #             status_code=401
        #         )
        #
        #     # Parse JWT claims
        #     user_info = _parse_jwt_token(token)
        #
        #     # Validate user_id in request body matches token user_id (CRITICAL SECURITY)
        #     if request.method in ["POST", "PUT", "DELETE", "GET"]:
        #         body_user_id = await self._extract_user_id_from_body(request)
        #         if body_user_id and body_user_id != user_info["user_id"]:
        #             return JSONResponse(
        #                 content={"message": "Access denied: Cannot access another user's resources"},
        #                 status_code=403
        #             )
        #     permission = await _determine_permission(request)
        #     if permission:
        #         vp = self.valid_package(permission, token)
        #         if vp["statusCode"] != 200:
        #             return JSONResponse(
        #                 content={"message": vp["message"]},
        #                 status_code=vp["statusCode"]
        #             )

        # Validate with IDP if URL is configured
        # if self.idp_url:
        # if permission:
        # is_valid = await self._validate_with_idp(token, permission)
        # if not is_valid:
        #     return JSONResponse(
        #         content={"message": self.error_messages["permission_denied"]},
        #         status_code=403
        #     )

        # Set user info in request state

    async def _extract_user_id_from_body(self, request: Request) -> Optional[str]:
        """Extract user_id from request body for validation"""
        try:
            if request.method == "GET":
                # For GET requests, user_id is typically in query params, not body
                return request.query_params.get("userId") or request.query_params.get("user_id")

            # Read body
            body = await request.body()

            # החזר את ה-body חזרה ל-request כדי שה-route יוכל להשתמש בו
            async def receive():
                return {"type": "http.request", "body": body}

            request._receive = receive
            if not body:
                return None

            # Parse JSON
            body_data = json.loads(body.decode('utf-8'))

            # Look for common user_id field names
            user_id_fields = ["userId", "user_id", "userID"]
            for field in user_id_fields:
                if field in body_data:
                    return body_data[field]

            return None

        except Exception as e:
            logger.warning(f"Could not extract user_id from request body: {e}")
            return None
