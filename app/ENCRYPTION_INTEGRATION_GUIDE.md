# Encryption Integration Guide

## Overview

This guide explains the integrated encryption system for chat history management. The system provides end-to-end encryption for conversation data with the following key features:

- **User-specific encryption keys** managed by <PERSON>r<PERSON><PERSON><PERSON>ana<PERSON>
- **Automatic conversation encryption** before MongoDB storage
- **Optional AWS KMS integration** with master key fallback
- **Transparent decryption** on chat retrieval
- **Data integrity verification** using SHA-256 hashes

## Architecture Components

### 1. EncryptionManager
- **Location**: `app/chat_history/security/encrypt_manger.py`
- **Purpose**: Core encryption/decryption using AES-GCM
- **Key Features**:
  - Singleton pattern for consistent key management
  - User-specific conversation encryption
  - SHA-256 hash generation and verification
  - Master key encryption for user keys

### 2. UserKeyManager
- **Location**: `app/chat_history/services/user_key_manager.py`
- **Purpose**: Manages user encryption keys
- **Key Features**:
  - Creates and stores encrypted user keys in MongoDB
  - Optional AWS KMS encryption for user keys
  - <PERSON><PERSON> caching for performance
  - Automatic fallback to master key encryption

### 3. ChatHistoryManager
- **Location**: `app/chat_history/services/encrypt_data_manager.py`
- **Purpose**: Integrates encryption into chat storage/retrieval
- **Key Features**:
  - Automatic encryption before MongoDB storage
  - Transparent decryption on retrieval
  - Audit logging for all operations
  - Backward compatibility with unencrypted data

### 4. AWSKMSService (Optional)
- **Location**: `app/chat_history/services/aws_kms_service.py`
- **Purpose**: AWS KMS integration for user key encryption
- **Key Features**:
  - Encrypts user keys using AWS KMS
  - Graceful fallback to master key encryption
  - Comprehensive error handling

## Integration Points

### 1. Application Startup
The encryption services are initialized in `main.py`:

```python
# Services are initialized on startup
await init_encryption_services()
```

### 2. Dependency Injection
Services are available through FastAPI dependencies:

```python
from api.dependencies.encryption_services import (
    get_encryption_manager_dep,
    get_user_key_manager_dep,
    get_chat_history_manager_dep
)
```

### 3. Chat History Endpoints
The `/chat/{chat_id}` endpoint automatically decrypts conversations:

```python
@history_router.get("/chat/{chat_id}")
async def get_chat(
    chat_id: str,
    user_id: str,
    chat_history_manager = Depends(get_chat_history_manager_dep)
):
    # Automatic decryption happens here
    chat_document = await get_chat_by_id(
        mongo_client, chat_id, user_id=user_id,
        chat_history_manager=chat_history_manager
    )
```

## Configuration

### Environment Variables

```bash
# Master key for encryption (32 bytes)
KMS_MASTER_KEY=your_32_byte_master_key_here

# AWS KMS Configuration (optional)
AWS_KMS_KEY_ID=your-kms-key-id
AWS_KMS_REGION=your-aws-region

# MongoDB Configuration
MONGO_URL=mongodb+srv://user:<EMAIL>/
MONGO_DB_HISTORY=your-history-db
MONGO_USERS_COLLECTION=users

# Cache TTL (seconds)
USER_KEY_CACHE_TTL=1800
```

### Encryption Modes

1. **KMS Mode**: User keys encrypted with AWS KMS
2. **Master Key Mode**: User keys encrypted with master key (fallback)

## Security Features

### Data Protection
- **AES-GCM Encryption**: Authenticated encryption for conversations
- **256-bit Keys**: Strong encryption keys for each user
- **Unique Nonces**: Each encryption uses a unique nonce
- **Hash Verification**: SHA-256 hashes ensure data integrity

### Key Management
- **User-specific Keys**: Each user has their own encryption key
- **Encrypted Storage**: User keys never stored in plaintext
- **Temporary Caching**: Decrypted keys cached for performance
- **Automatic Expiration**: Cache entries expire after TTL

### Access Control
- **User Validation**: Only key owners can decrypt their data
- **Audit Logging**: All operations logged for compliance
- **Error Handling**: Graceful degradation on encryption failures

## Testing

### 1. Integration Test Script
Run the comprehensive test:

```bash
cd app
python test_encryption_integration.py
```

### 2. API Test Endpoints (Development Only)
Available in development mode:

- `POST /encryption-test/create-user-key/{user_id}` - Create user key
- `GET /encryption-test/user-key-status/{user_id}` - Check key status
- `GET /encryption-test/encryption-status` - System status
- `POST /encryption-test/test-conversation-encryption/{user_id}` - Test encryption

### 3. Manual Testing
```bash
# Check encryption status
curl http://localhost:8001/encryption-test/encryption-status

# Create user key
curl -X POST http://localhost:8001/encryption-test/create-user-key/test_user_123

# Test conversation encryption
curl -X POST http://localhost:8001/encryption-test/test-conversation-encryption/test_user_123
```

## Workflow

### 1. User Key Creation
1. User makes first request requiring encryption
2. UserKeyManager generates 256-bit key
3. Key encrypted with KMS (if available) or master key
4. Encrypted key stored in MongoDB users collection

### 2. Conversation Storage
1. Chat data received from Redis
2. User key retrieved and decrypted
3. Conversation encrypted with user key
4. Hash generated for integrity verification
5. Encrypted data stored in MongoDB

### 3. Conversation Retrieval
1. Chat requested by user
2. User ownership validated
3. User key retrieved and decrypted
4. Conversation decrypted with user key
5. Hash verified for integrity
6. Decrypted conversation returned

## Monitoring

### Logs
- All encryption operations logged with audit decorators
- Performance metrics for key retrieval
- Error tracking for failed operations

### Health Checks
- Encryption service availability
- KMS connectivity (if configured)
- MongoDB connectivity
- Redis cache status

## Troubleshooting

### Common Issues

1. **EncryptionManager not initialized**
   - Ensure `init_encryption_services()` called on startup
   - Check master key configuration

2. **KMS errors**
   - Verify AWS credentials and permissions
   - Check KMS key ID and region
   - System falls back to master key encryption

3. **User key not found**
   - Key created automatically on first use
   - Check MongoDB connectivity
   - Verify user ID format

4. **Decryption failures**
   - Verify user ownership of chat
   - Check data integrity
   - Review audit logs for errors

### Performance Optimization

1. **Key Caching**: Decrypted keys cached in Redis
2. **Batch Operations**: Multiple conversations processed efficiently
3. **Lazy Loading**: Services initialized only when needed
4. **Connection Pooling**: MongoDB connections reused

## Migration Strategy

### Phase 1: Deployment (Current)
- New conversations automatically encrypted
- Existing conversations remain accessible
- Backward compatibility maintained

### Phase 2: Background Migration (Future)
- Encrypt existing plain conversations
- Verify data integrity
- Update audit logs

### Phase 3: Cleanup (Future)
- Remove plain conversation fields
- Make encryption mandatory
- Update schema validation

## Security Considerations

1. **Never store unencrypted conversation data in MongoDB**
2. **User keys never stored in plaintext**
3. **Master key must be 32 bytes and securely managed**
4. **KMS permissions should follow least privilege**
5. **Audit logs should be monitored for anomalies**
6. **Cache TTL should balance security and performance**
